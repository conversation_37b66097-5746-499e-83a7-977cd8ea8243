from pycnp import Model
from pycnp.graph_visualization import visualize_graph
from pycnp.stop import NoImprovement

# Method 2: Use add_node and add_edge methods to build the graph structure
model = Model()

# Add 20 nodes (0-19) 
for i in range(20):
    model.add_node(i)

edges = [
    (4, 0), (4, 1), (4, 2), (4, 3), (9, 5), 
    (9, 6), (9, 7), (9, 8),(14, 10), (14, 11), 
    (14, 12), (14, 13), (19, 15), (19, 16), 
    (19, 17), (19, 18), (4, 9), (9, 14),
    (14, 19), (2, 6), (7, 11), (12, 16)]

for u, v in edges:
    model.add_edge(u, v)

# Set a stopping condition based on no improvement
criterion = NoImprovement(20)

# Set problem type to CNP
problem_type = "CNP"
bound = 3 
seed = 6

result= model.solve(problem_type, bound, criterion, seed)

# Visualize the graph
visualize_graph(
    problem_data=model.getProblemData(),
    critical_nodes_set=result.best_solution,
)
