#include "CHNSStrategy.h"
#include "Graph/CNP_Graph.h"
#include "Graph/NWCNP_Graph.h"
#include "SearchResult.h"

CHNSStrategy::CHNSStrategy(
    Graph &graph, const std::unordered_map<std::string, std::any> &params)
    : graph(graph), params(params)
{

    auto it = params.find("maxIdleSteps");
    if (it != params.end())
    {
        try
        {
            maxIdleSteps = std::any_cast<int>(it->second);
        }
        catch (const std::bad_any_cast &)
        {
        }
    }

    it = params.find("theta");
    if (it != params.end())
    {
        try
        {
            theta = std::any_cast<double>(it->second);
        }
        catch (const std::bad_any_cast &)
        {
        }
    }

    it = params.find("seed");
    if (it != params.end())
    {
        try
        {
            int seed = std::any_cast<int>(it->second);

            if (seed > 0)
            {
                rng.setSeed(seed);
            }
        }
        catch (const std::bad_any_cast &)
        {
        }
    }
}

SearchResult CHNSStrategy::execute()
{

    SearchResult result;

    Graph &currentGraph = graph;
    NodeSet bestSolution;

    if (auto *cnpGraph = dynamic_cast<CNP_Graph *>(&currentGraph))
    {
        bestSolution = cnpGraph->removedNodes;
    }
    else if (auto *nwcnpGraph = dynamic_cast<NWCNP_Graph *>(&currentGraph))
    {
        bestSolution = nwcnpGraph->removedNodes;
    }

    int currentObjValue = graph.getObjectiveValue();
    int bestObjValue = currentObjValue;
    long numSteps = 0;
    long numIdleSteps = 0;

    while (numIdleSteps < maxIdleSteps)
    {
        numSteps++;

        performMove(currentGraph, currentObjValue, numSteps);

        if (currentObjValue < bestObjValue)
        {
            bestSolution = currentGraph.getRemovedNodes(); // 获取当前最优解（现在是引用拷贝）
            bestObjValue = currentObjValue;
            numIdleSteps = 0;
        }
        else
        {
            numIdleSteps++;
        }
    }

    result.solution = bestSolution;
    result.objValue = bestObjValue;
    return result;
}

void CHNSStrategy::performMove(Graph &currentGraph,
                               int &currentObjValue,
                               int numSteps)
{

    if (auto *nwcnpGraph = dynamic_cast<NWCNP_Graph *>(&currentGraph))
    {

        int destroy_num = 5;
        while (destroy_num > 0)
        {
            destroy_num--;
            Node nodeToRemove;

            ComponentIndex componentToRemove
                = currentGraph.selectRemovedComponent();

            if (rng.generateProbability() < theta)
            {

                nodeToRemove = currentGraph.impactSelectNodeFromComponent(
                    componentToRemove);
            }
            else
            {

                nodeToRemove = currentGraph.ageSelectNodeFromComponent(
                    componentToRemove);
            }

            currentGraph.removeNode(nodeToRemove);
            currentGraph.setNodeAge(nodeToRemove, numSteps);
        }

        while (nwcnpGraph->SolWeight > nwcnpGraph->bound)
        {

            Node nodeToAdd = nwcnpGraph->greedySelectNodeToAdd();

            nwcnpGraph->addNode(nodeToAdd);
            currentObjValue = nwcnpGraph->getObjectiveValue();
            currentGraph.setNodeAge(nodeToAdd, numSteps);
        }
    }
    else
    {
        Node nodeToRemove;

        ComponentIndex componentToRemove
            = currentGraph.selectRemovedComponent();

        if (rng.generateProbability() < theta)
        {

            nodeToRemove
                = currentGraph.impactSelectNodeFromComponent(componentToRemove);
        }
        else
        {

            nodeToRemove
                = currentGraph.ageSelectNodeFromComponent(componentToRemove);
        }

        currentGraph.removeNode(nodeToRemove);
        currentGraph.setNodeAge(nodeToRemove, numSteps);

        Node nodeToAdd = currentGraph.greedySelectNodeToAdd();

        if (nodeToAdd != Graph::INVALID_NODE)
        {
            currentGraph.addNode(nodeToAdd);
            currentGraph.setNodeAge(nodeToAdd, numSteps);
        }
    }

    currentObjValue = currentGraph.getObjectiveValue();
}