Examples and Tutorials
======================

This section provides complete usage examples for PyCNP, covering various problem types and application scenarios.

.. toctree::
   :maxdepth: 2

   example_usage

Basic Examples
--------------

**Quick Start Example**

.. literalinclude:: example-.py
   :language: python
   :caption: Basic CNP problem solving example

**Model Building Example**

.. literalinclude:: model-.py
   :language: python
   :caption: Complete model building and solving workflow

Data Files
----------

Example data files contain graph instances of different scales and types:

**Small-scale Test Graphs**
- :download:`ER250.txt <ER250.txt>` - 250-node Erd<PERSON>s-<PERSON><PERSON>yi random graph
- :download:`ER500.txt <ER500.txt>` - 500-node Erd<PERSON>s-Rényi random graph

**Medium-scale Graphs**
- :download:`BA500.txt <BA500.txt>` - 500-node Barabási-Albert scale-free graph
- :download:`ErdosRenyi_n941.txt <ErdosRenyi_n941.txt>` - 941-node <PERSON>rd<PERSON>s-Rényi graph

**Weighted Graph Examples**
- :download:`<PERSON>rdosRenyi_n941_weight.txt <ErdosRenyi_n941_weight.txt>` - Corresponding node weight file

**Real Networks**
- :download:`hi_tech.txt <hi_tech.txt>` - High-tech company collaboration network

Usage Instructions
------------------

1. **Download Data Files**: Click the links above to download required graph data files
2. **Run Example Code**: Copy example code to local files and run
3. **Modify Parameters**: Adjust algorithm parameters and stopping criteria as needed

.. note::

   All examples have been tested and can be run directly. If you encounter issues, please refer to the :doc:`../setup/faq` page.

More Examples
-------------

More advanced examples and application cases are being prepared, stay tuned.

If you have specific application scenarios that need example support, feel free to suggest through GitHub Issues.
