常见问题解答 (FAQ)
==================

本页面收集了 PyCNP 用户最常遇到的问题和解答。

安装相关
--------

**Q: 安装 PyCNP 时出现编译错误怎么办？**

A: PyCNP 提供预编译的二进制包，通常不需要编译。如果遇到问题：

1. 确保 Python 版本 >= 3.8
2. 升级 pip: ``pip install --upgrade pip``
3. 尝试清除缓存: ``pip cache purge``
4. 如果仍有问题，请在 GitHub 上报告

**Q: 支持哪些操作系统？**

A: PyCNP 支持：

- Windows 10/11 (x64)
- macOS 10.15+ (Intel 和 Apple Silicon)
- Linux (x64) with glibc 2.17+

**Q: 可以在 Jupyter Notebook 中使用吗？**

A: 是的，PyCNP 完全支持 Jupyter Notebook 和 JupyterLab。

使用相关
--------

**Q: 如何选择合适的问题类型？**

A: 根据您的具体需求：

- **CNP**: 标准问题，所有节点权重相等
- **NWCNP**: 节点有不同权重，需要考虑预算约束
- **DCNP**: 只关心特定距离内的连通性

**Q: bound 参数应该设置多大？**

A: 建议的经验值：

- **CNP**: 图节点数的 5-20%
- **NWCNP**: 根据权重分布，通常是总权重的 10-30%
- **DCNP**: 与 CNP 类似，但需要考虑距离参数 K 的影响

**Q: 如何选择停止准则？**

A: 不同场景的建议：

- **快速测试**: ``MaxRuntime(30)`` (30秒)
- **日常使用**: ``MaxRuntime(300)`` (5分钟)
- **高质量求解**: ``MaxRuntime(1800)`` (30分钟)
- **研究用途**: ``NoImprovement(100)`` 或更长时间

**Q: 为什么每次运行结果不同？**

A: PyCNP 使用随机算法。要获得可重现的结果，请设置固定的随机种子：

.. code-block:: python

   result = model.solve(..., seed=42)

性能相关
--------

**Q: PyCNP 能处理多大的图？**

A: PyCNP 已在以下规模的图上测试：

- 节点数: 最多 100,000
- 边数: 最多 1,000,000
- 内存使用: 通常 < 2GB

**Q: 如何提高求解速度？**

A: 几个建议：

1. 使用较短的停止时间进行初步测试
2. 对于大图，考虑使用更小的 bound 值
3. 确保系统有足够的内存
4. 使用多核 CPU（PyCNP 会自动利用）

**Q: 求解过程可以中断吗？**

A: 是的，使用 Ctrl+C 可以安全中断求解过程。当前最佳解会被保留。

数据格式相关
------------

**Q: 支持哪些图文件格式？**

A: 目前支持：

- 邻接表格式（推荐）
- 边列表格式（部分支持）

**Q: 节点编号必须从 0 开始吗？**

A: 是的，PyCNP 要求节点编号从 0 开始，连续编号到 n-1。

**Q: 如何处理有向图？**

A: PyCNP 目前只支持无向图。有向图需要先转换为无向图。

**Q: 权重文件的格式是什么？**

A: 权重文件每行一个数值，对应节点 0, 1, 2, ... 的权重：

.. code-block:: text

   1.5
   2.0
   1.0
   3.5

结果解释
--------

**Q: 目标函数值代表什么？**

A: 不同问题类型的目标函数：

- **CNP**: 剩余图中所有连通分量的内部连接数总和
- **NWCNP**: 与 CNP 相同
- **DCNP**: 距离不超过 K 的节点对数量

**Q: 如何验证解的正确性？**

A: 可以手动验证：

.. code-block:: python

   # 检查解的约束满足情况
   solution = result.best_solution
   
   # 对于 CNP: 检查节点数量
   assert len(solution) <= bound
   
   # 对于 NWCNP: 检查权重总和
   total_weight = sum(problem_data.get_node_weight(node) for node in solution)
   assert total_weight <= bound

**Q: 运行时间包括什么？**

A: 运行时间包括：

- 算法执行时间
- 不包括数据加载时间
- 不包括结果后处理时间

错误处理
--------

**Q: 遇到 "InvalidArgumentError" 怎么办？**

A: 这通常表示参数设置有误：

- 检查 bound 值是否为正数
- 确保问题类型字符串正确 ("CNP", "NWCNP", "DCNP")
- 验证文件路径是否正确

**Q: 遇到 "RuntimeError" 怎么办？**

A: 这可能表示：

- 内存不足
- 图数据格式错误
- 系统资源限制

请检查错误信息中的具体描述。

**Q: 程序崩溃怎么办？**

A: 如果遇到程序崩溃：

1. 记录错误信息和复现步骤
2. 检查输入数据的格式
3. 尝试使用更小的测试数据
4. 在 GitHub 上报告问题

高级使用
--------

**Q: 如何自定义算法参数？**

A: 使用 ``MemeticSearch`` 类进行高级配置：

.. code-block:: python

   from pycnp import MemeticSearch
   from pycnp._pycnp import MSParams, VPParams
   
   # 自定义参数
   ms_params = MSParams(
       search="CHNS",
       crossover="RSC",
       is_pop_variable=True,
       initial_pop_size=10
   )

   vp_params = VPParams(
       max_pop_size=30,
       increase_pop_size=5,
       max_idle_gens=15
   )
   
   ms = MemeticSearch(problem_data, "CNP", bound, seed)
   # 使用自定义参数...

**Q: 如何获取详细的统计信息？**

A: 在求解时启用统计收集：

.. code-block:: python

   result = model.solve(..., collect_stats=True)
   
   # 访问统计信息
   print(result.statistics)

**Q: 支持并行计算吗？**

A: PyCNP 的 C++ 核心会自动利用多核 CPU。Python 层面目前不支持显式的并行接口。

获取帮助
--------

如果您的问题没有在这里找到答案：

1. 查看 :doc:`../examples/index` 中的示例
2. 阅读 :doc:`../api/pycnp` API 文档
3. 在 `GitHub Issues <https://github.com/xuebo100/PyCNP/issues>`_ 上搜索或提问
4. 发送邮件到项目维护者

报告问题时，请包含：

- PyCNP 版本
- Python 版本和操作系统
- 完整的错误信息
- 最小的复现示例
