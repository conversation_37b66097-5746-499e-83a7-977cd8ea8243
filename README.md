# PyCNP: High-Performance Critical Node Problem Solver

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![C++17](https://img.shields.io/badge/C++-17-blue.svg)](https://en.cppreference.com/w/cpp/17)

PyCNP is a high-performance Critical Node Problem (CNP) solver that provides advanced algorithms through a Python interface. This library combines the high performance of C++ implementation with the ease of use of Python, offering researchers and engineers powerful graph optimization tools.

## 🚀 Key Features

- **High Performance**: C++ core algorithms with Python bindings via pybind11
- **Multi-Problem Support**: Supports CNP, DCNP, NWCNP and other problem variants
- **Rich Algorithms**: Integrates multiple advanced search strategies and crossover operators
- **Easy to Use**: Provides clean Python API with comprehensive documentation
- **Extensible**: Modular design makes it easy to add new algorithms and strategies

## 📋 Supported Problem Types

| Problem Type | Description | Application Scenarios |
|-------------|-------------|----------------------|
| **CNP** | Standard Critical Node Problem | Network robustness analysis, social network analysis |
| **DCNP** | Distance-Constrained Critical Node Problem | Communication network optimization, propagation control |
| **NWCNP** | Node-Weighted Critical Node Problem | Resource-constrained network optimization |

## 🔧 Algorithm Components

### Search Strategies
- **CBNS**: Component-Based Neighborhood Search
- **CHNS**: Critical Hit Neighborhood Search
- **DLAS**: Diversified Local Adaptive Search
- **BCLS**: Best Component Local Search

### Crossover Operators
- **DB**: Double Backbone crossover
- **RSC**: Reduce-Solve-Combine crossover
- **IRR**: Inherit-Repair-Recombination crossover

### Stopping Criteria
- Maximum iteration limit
- Runtime limit
- Solution quality stagnation detection

## Installation Requirements

- Python 3.8+
- Poetry (for dependency management)
- Meson (build system)
- Ninja (build backend)
- C++20 compatible compiler
- pybind11 (Python-C++ bindings)

## Installation

### Using Poetry (Recommended)

1. **Clone the repository**:

   ```bash
   git clone https://github.com/xuebo100/PyCNP.git
   cd PyCNP
   ```

2. **Install Poetry** (if not already installed):

   ```bash
   curl -sSL https://install.python-poetry.org | python3 -
   ```

3. **Install project dependencies**:

   ```bash
   poetry install
   ```

4. **Build C++ extensions**:

   ```bash
   poetry run meson setup build
   poetry run meson compile -C build
   ```

5. **Verify installation**:

   ```bash
   poetry run python -c "import pycnp; print('PyCNP installation successful!')"
   ```

### Development Environment Setup

If you want to contribute to development, you can install development dependencies:

```bash
# Install development dependencies
poetry install --with dev

# Run code formatting
poetry run black pycnp/
poetry run isort pycnp/

# Run tests (if available)
poetry run pytest
```

## Project Structure

```text
PyCNP/
├── pyproject.toml          # Poetry configuration file
├── meson.build            # Meson build configuration
├── pycnp/                 # Python package
│   ├── __init__.py
│   ├── src/               # C++ source code
│   │   ├── pybind.cpp     # Python bindings
│   │   ├── Graph/         # Graph-related classes
│   │   └── search/        # Search algorithms
│   └── *.py              # Python modules
├── examples/              # Example files
├── docs/                  # Documentation
└── README.md
```
