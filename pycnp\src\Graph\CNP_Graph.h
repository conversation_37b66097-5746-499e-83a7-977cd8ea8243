#ifndef CNP_GRAPH_H
#define CNP_GRAPH_H

#include "../RandomNumberGenerator.h"
#include "Graph.h"
#include <cctype>
#include <fstream>
#include <iostream>
#include <memory>
#include <queue>
#include <set>
#include <string>
#include <unordered_set>
#include <vector>

/**
 * CNP_Graph
 *
 * Implementation of the Critical Node Problem (CNP) graph model.
 *
 * Implements the graph structure and specific algorithms for the CNP problem,
 * with the goal of minimizing the number of connected pairs after removing
 * a given number of nodes.
 */
class CNP_Graph : public Graph
{
private:
    int numNodes;              ///< Number of vertices
    NodeSet originalNodesSet;  ///< Set of all existing nodes
    std::vector<Age> nodeAge;  ///< Node "age"

    std::vector<NodeSet> currentAdjList;   ///< Current adjacency list
    std::vector<NodeSet> originalAdjList;  ///< Original adjacency list

    int numToRemove;  ///< Number of nodes to remove
    std::vector<ComponentIndex>
        nodeToComponentIndex;  ///< Stores the component index for each vertex
    std::vector<Component> connectedComponents;  ///< Current graph's connected components
    int connectedPairs;
    RandomNumberGenerator rng;  ///< Random number generator

    /**
     * Selects the larger component to remove.
     *
     * Returns
     * -------
     * ComponentIndex
     *     Component index.
     */
    ComponentIndex selectRemovedLargerComponent() const;

    /**
     * Calculates connection gain after adding a node.
     *
     * Parameters
     * ----------
     * node
     *     Node to add.
     *
     * Returns
     * -------
     * int
     *     Connection gain value.
     */
    int calculateConnectionGain(Node node) const override;

    // Tarjan algorithm auxiliary variables
    mutable std::vector<int> dfn;         ///< Node discovery time
    mutable std::vector<int> lowVec;      ///< Minimum discovery time reachable by node
    mutable std::vector<int> stSizeVec;   ///< Subtree size
    mutable std::vector<int> cutSizeVec;  ///< Cut size
    mutable std::vector<int> impactVec;   ///< Node impact
    mutable std::vector<int> flagVec;     ///< Flag vector
    mutable std::vector<bool> isCutVec;   ///< Whether node is a cut vertex
    mutable int timeStamp;                ///< Timestamp
    mutable int nodeRoot;                 ///< Root node

    /**
     * Implementation of Tarjan algorithm in connected component.
     *
     * Parameters
     * ----------
     * compIndex
     *     Component index.
     * nodeIdx
     *     Node index.
     * nodeToIdx
     *     Node to index mapping.
     */
    void tarjanInComponent(ComponentIndex compIndex,
                        int nodeIdx,
                        const std::vector<int> &nodeToIdx) const;

public:
    NodeSet removedNodes;  ///< Set of removed vertices

    /**
     * Creates a deep copy of the current CNP_Graph object.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     Smart pointer to newly created graph object.
     */
    std::unique_ptr<Graph> clone() const override;

    /**
     * Constructor.
     *
     * Parameters
     * ----------
     * nodes
     *     Set of nodes.
     * adjList
     *     Adjacency list.
     * bound
     *     Number of nodes to remove.
     * seed
     *     Random number seed.
     */
    CNP_Graph(NodeSet nodes, std::vector<NodeSet> adjList, int bound, int seed);

    /**
     * Default constructor.
     */
    CNP_Graph() {};

    /**
     * Updates graph based on removed nodes.
     *
     * Parameters
     * ----------
     * removedNodes
     *     Set of removed nodes.
     */
    void updateGraphByRemovedNodes(const NodeSet &removedNodes) override;
    
    /**
     * Reduces graph size based on removed nodes.
     *
     * Parameters
     * ----------
     * nodesToRemove
     *     Set of nodes to remove.
     */
    void getReducedGraphByRemovedNodes(const NodeSet &nodesToRemove) override;

    /**
     * Adds a node to the graph.
     *
     * Parameters
     * ----------
     * node
     *     Node to add.
     */
    void addNode(Node node) override;

    /**
     * Removes a node from the graph.
     *
     * Parameters
     * ----------
     * node
     *     Node to remove.
     */
    void removeNode(Node node) override;

    /**
     * Checks if a node has been removed.
     *
     * Parameters
     * ----------
     * node
     *     Node to check.
     *
     * Returns
     * -------
     * bool
     *     True if node has been removed.
     */
    bool isNodeRemoved(Node node) const override
    {
        return removedNodes.find(node) != removedNodes.end();
    }

    /**
     * Gets total number of nodes in the graph.
     *
     * Returns
     * -------
     * int
     *     Total number of nodes.
     */
    int getNumNodes() const override { return numNodes; }

    /**
     * Gets set of all removed nodes.
     *
     * Returns
     * -------
     * const NodeSet&
     *     Constant reference to set of removed nodes.
     */
    const NodeSet& getRemovedNodes() const override { return removedNodes; }

    /**
     * Sets node age.
     *
     * Parameters
     * ----------
     * node
     *     Node ID.
     * age
     *     Age value.
     */
    void setNodeAge(Node node, Age age) override { nodeAge[node] = age; }

    /**
     * Gets current graph's objective function value.
     *
     * Returns
     * -------
     * int
     *     Objective function value (number of connected pairs).
     */
    int getObjectiveValue() const override { return connectedPairs; }

    /**
     * Converts to random feasible solution.
     *
     * Randomly selects nodes from current graph until reaching the number
     * of nodes to remove, generating a feasible solution.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     Pointer to current graph instance.
     */
    std::unique_ptr<Graph> getRandomFeasibleGraph() override;

    /**
     * Initializes components and node mapping.
     */
    void initializeComponentsAndMapping();

    /**
     * Finds connected component using DFS.
     *
     * Parameters
     * ----------
     * startNode
     *     Starting node.
     *
     * Returns
     * -------
     * Component
     *     Connected component.
     */
    Component DFSfindComponent(Node startNode) const;

    /**
     * Selects component to remove.
     *
     * Returns
     * -------
     * ComponentIndex
     *     Component index.
     */
    ComponentIndex selectRemovedComponent() const override;

    /**
     * Randomly selects node to remove from specified component.
     *
     * Parameters
     * ----------
     * componentIndex
     *     Component index.
     *
     * Returns
     * -------
     * Node
     *     Node ID to remove.
     */
    Node
    randomSelectNodeFromComponent(ComponentIndex componentIndex) const override;

    /**
     * Selects node to remove from component based on weight.
     *
     * Parameters
     * ----------
     * componentIndex
     *     Component index.
     *
     * Returns
     * -------
     * Node
     *     Node ID to remove.
     */
    Node
    ageSelectNodeFromComponent(ComponentIndex componentIndex) const override;

    /**
     * Selects node with minimum impact to remove from component.
     *
     * Parameters
     * ----------
     * componentIndex
     *     Component index.
     *
     * Returns
     * -------
     * Node
     *     Node ID to remove.
     */
    Node
    impactSelectNodeFromComponent(ComponentIndex componentIndex) const override;

    /**
     * Selects node to add back to graph.
     *
     * Returns
     * -------
     * Node
     *     Node ID to add.
     */
    Node greedySelectNodeToAdd() const override;

    /**
     * Randomly selects a node to remove.
     *
     * Returns
     * -------
     * Node
     *     Node ID to remove.
     */
    Node randomSelectNodeToRemove() const override;
};

#endif
