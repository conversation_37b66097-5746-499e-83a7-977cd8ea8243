<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00BCD4;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#grad1)" stroke="#1976D2" stroke-width="2"/>
  
  <!-- 网络节点 -->
  <circle cx="20" cy="20" r="3" fill="white" opacity="0.9"/>
  <circle cx="44" cy="20" r="3" fill="white" opacity="0.9"/>
  <circle cx="32" cy="32" r="4" fill="#FF5722" stroke="white" stroke-width="1"/>
  <circle cx="20" cy="44" r="3" fill="white" opacity="0.9"/>
  <circle cx="44" cy="44" r="3" fill="white" opacity="0.9"/>
  
  <!-- 连接线 -->
  <line x1="20" y1="20" x2="32" y2="32" stroke="white" stroke-width="2" opacity="0.7"/>
  <line x1="44" y1="20" x2="32" y2="32" stroke="white" stroke-width="2" opacity="0.7"/>
  <line x1="20" y1="44" x2="32" y2="32" stroke="white" stroke-width="2" opacity="0.7"/>
  <line x1="44" y1="44" x2="32" y2="32" stroke="white" stroke-width="2" opacity="0.7"/>
  <line x1="20" y1="20" x2="44" y2="20" stroke="white" stroke-width="1" opacity="0.5"/>
  <line x1="20" y1="44" x2="44" y2="44" stroke="white" stroke-width="1" opacity="0.5"/>
  
  <!-- 文字 CNP -->
  <text x="32" y="56" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="white">CNP</text>
</svg>
