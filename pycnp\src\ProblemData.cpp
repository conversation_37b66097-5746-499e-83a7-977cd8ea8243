#include "ProblemData.h"
#include "Graph/CNP_Graph.h"
#include "Graph/DCNP_Graph.h"
#include "Graph/NWCNP_Graph.h"

ProblemData::ProblemData(int num)
{
    numNodes_ = num;
    NodesSet.reserve(num);
    AdjList.resize(num);
}

void ProblemData::addNode(Node node)
{
    NodesSet.insert(node);
    nodeWeight[node] = 0.0;
}

void ProblemData::addNodeWeight(Node node, double weight)
{
    if (node >= 0 && node < static_cast<int>(nodeWeight.size()))
    {
        nodeWeight[node] = weight;
    }
}

void ProblemData::addEdge(Node u, Node v)
{
    AdjList[u].insert(v);
    AdjList[v].insert(u);
}

double ProblemData::getNodeWeight(Node node) const
{
    if (node >= 0 && node < static_cast<int>(nodeWeight.size()))
    {
        return nodeWeight.at(node);
    }
    return -1;
}

ProblemData ProblemData::readFromAdjacencyListFile(const std::string &filename)
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        throw std::runtime_error("Cannot open file: " + filename);
    }

    int numNodes;
    file >> numNodes;
    if (file.eof())
    {
        throw std::runtime_error("File format error: empty file");
    }

    ProblemData problemData(numNodes);

    for (int v1 = 0; v1 < numNodes; v1++)
    {
        int node;
        file >> node;

        problemData.NodesSet.insert(node);

        char c;
        while ((c = file.get()) != ':' && std::isspace(c))
            ;
        bool nextLine = false;

        while (!file.eof() && !nextLine)
        {

            while (std::isspace(file.peek()))
            {
                nextLine = (file.get() == '\n');
            }
            if (!nextLine)
            {
                int node2;
                file >> node2;

                problemData.AdjList[node].insert(node2);
            }
        }
    }
    file.close();
    return problemData;
}

void ProblemData::readNodeWeightsFromFile(const std::string &filename)
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        throw std::runtime_error("Cannot open weight file: " + filename);
    }
    double w;

    for (size_t i = 0; i < static_cast<size_t>(numNodes()); i++)
    {
        file >> w;
        nodeWeight[i] = w;
    }
    file.close();
}

ProblemData ProblemData::readFromEdgeListFormat(const std::string &filename)
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        throw std::runtime_error("Cannot open file: " + filename);
    }

    char bidon[50];
    char StrReading[100];
    file >> StrReading;

    if (file.eof())
    {
        throw std::runtime_error("File format error: empty file");
    }

    int numNodes = 0;
    int numEdges = 0;

    while (!file.eof())
    {
        if (strcmp(StrReading, "p") == 0)
        {

            file >> bidon >> numNodes >> numEdges;
            break;
        }
        file >> StrReading;
    }

    ProblemData problemData(numNodes);

    for (int i = 0; i < numNodes; i++)
    {
        problemData.NodesSet.insert(i);
    }

    int x1, x2;
    int linecount = 0;

    while (!file.eof())
    {
        if (strcmp(StrReading, "e") == 0)
        {

            file >> x1 >> x2;
            linecount++;

            if (x1 < 0 || x2 < 0 || x1 >= numNodes || x2 >= numNodes)
            {
                throw std::runtime_error(
                    "Node index error: x1=" + std::to_string(x1)
                    + ", x2=" + std::to_string(x2)
                    + ", line number=" + std::to_string(linecount));
            }

            problemData.AdjList[x1].insert(x2);
            problemData.AdjList[x2].insert(x1);
        }
        file >> StrReading;
    }

    file.close();

    for (size_t i = 0; i < static_cast<size_t>(problemData.numNodes()); i++)
    {
        problemData.nodeWeight[i] = 0.0;
    }

    return problemData;
}

std::unique_ptr<Graph> ProblemData::createOriginalGraph(
    const std::string &problemType, int numToRemove, int seed, int K) const
{
    std::unique_ptr<Graph> graph;

    if (problemType == "CNP")
    {

        if (numToRemove > static_cast<int>(NodesSet.size()))
        {
            throw std::runtime_error("The number of nodes to remove cannot be greater than the total number of nodes");
        }
        graph
            = std::make_unique<CNP_Graph>(NodesSet, AdjList, numToRemove, seed);
    }
    else if (problemType == "DCNP")
    {

        if (numToRemove > static_cast<int>(NodesSet.size()))
        {
            throw std::runtime_error("The number of nodes to remove cannot be greater than the total number of nodes");
        }
        graph = std::make_unique<DCNP_Graph>(
            NodesSet, K, AdjList, numToRemove, seed);
    }
    else if (problemType == "NWCNP")
    {

        double totalWeight = 0.0;
        for (const auto &weight : nodeWeight)
        {
            totalWeight += weight.second;
        }
        if (numToRemove > totalWeight)
        {
            throw std::runtime_error(
                "The total weight of nodes to remove cannot be greater than the total weight of all nodes");
        }
        graph = std::make_unique<NWCNP_Graph>(
            NodesSet, nodeWeight, AdjList, numToRemove, seed);
    }
    else
    {

        throw std::runtime_error("Unknown problem type: " + problemType);
    }
    return graph;
}