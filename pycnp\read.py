import os
from typing import Optional
from ._pycnp import ProblemData 
import inspect


def read(graph_file: str, weight_file: Optional[str] = None) -> 'ProblemData':
    """
    Read graph and weight files and return a ProblemData object
    
    Automatically detects graph file format (adjacency list or edge list) and reads data accordingly.
    If a weight file is provided, node weights are also read.
    
    Parameters:
        graph_file: Path to graph file (adjacency list or edge list format)
        weight_file: Path to weight file (optional)
        
    Returns:
        ProblemData object
        
    Raises:
        RuntimeError: When file cannot be opened or has invalid format
        FileNotFoundError: When file does not exist
    """
    # Get directory of caller script
    caller_frame = inspect.stack()[1]
    caller_path = caller_frame.filename
    caller_dir = os.path.dirname(os.path.abspath(caller_path))

    # If file paths are not absolute, prepend caller directory
    if not os.path.isabs(graph_file):
        graph_file = os.path.join(caller_dir, graph_file)
    if weight_file and not os.path.isabs(weight_file):
        weight_file = os.path.join(caller_dir, weight_file)

    if not os.path.exists(graph_file):
        raise FileNotFoundError(f"Graph file not found: {graph_file}")
    
    # Determine file format (adjacency list or edge list)
    with open(graph_file, 'r', encoding='utf-8') as f:
        first_line = f.readline().strip()
        
        # Check if first line contains 'p', which is characteristic of edge list format
        if first_line.startswith('p') or 'p' in first_line.split():
            # Edge list format
            problem_data = read_edge_list_format(graph_file)
        else:
            # Adjacency list format
            problem_data = read_adjacency_list_format(graph_file)
    
    # If weight file provided, read node weights
    if weight_file and os.path.exists(weight_file):
        try:
            problem_data.read_node_weights_from_file(weight_file)
        except Exception as e:
            raise RuntimeError(f"Failed to read weight file: {e}")
    
    return problem_data


def read_adjacency_list_format(filename: str) -> 'ProblemData':
    """
    Read data from adjacency list format file
    
    Adjacency list format: each line represents a node and its neighbors,
    formatted as "node_id neighbor1 neighbor2 ..."
    
    Parameters:
        filename: Path to file
        
    Returns:
        Initialized ProblemData object
        
    Raises:
        RuntimeError: When file cannot be opened or has invalid format
    """
    try:
        # Call C++ binding function to read adjacency list file
        return ProblemData.read_from_adjacency_list_file(filename)
    except Exception as e:
        raise RuntimeError(f"Failed to read adjacency list file: {e}")
    

def read_edge_list_format(filename: str) -> 'ProblemData':
    """
    Read data from edge list format file
    
    Edge list format: typically contains a header line "p edge num_nodes num_edges",
    followed by lines representing edges formatted as "e node1 node2"
    
    Parameters:
        filename: Path to file
        
    Returns:
        Initialized ProblemData object
        
    Raises:
        RuntimeError: When file cannot be opened or has invalid format
    """
    try:
        # Call C++ binding function to read edge list file
        return ProblemData.read_from_edge_list_format(filename)
    except Exception as e:
        raise RuntimeError(f"Failed to read edge list file: {e}")


def get_node_weight(problem_data: 'ProblemData', node: int) -> float:
    """
    Get weight of specified node
    
    Parameters:
        problem_data: ProblemData object
        node: Node ID
        
    Returns:
        Node weight, or -1 if node does not exist
    """
    try:
        return problem_data.get_node_weight(node)
    except Exception as e:
        return -1.0 