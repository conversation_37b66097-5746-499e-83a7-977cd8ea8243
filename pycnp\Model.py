"""
Model module

Provides modeling and solving classes for graph problems, integrating solving algorithms for various graph types.
"""

from typing import Set, Dict, List, Union, Any, Tuple, Optional

from ._pycnp import ProblemData
from pycnp.stop import StoppingCriterion
from pycnp.MemeticSearch import MemeticSearch, MemeticSearchParams, VariablePopulationParams
from pycnp.Result import Result


class Model:
    """
    Graph problem modeling and solving class

    Integrates creation, configuration, and invocation of solving algorithms for different types of graphs.
    Provides a unified interface for building graph models, configuring solving parameters, and executing solutions.

    Attributes
    ----------
    nodes : set
        Set of nodes in the graph
    adj_list : list
        Graph structure represented as adjacency list
    node_weights : dict
        Dictionary of node weights
    """

    def __init__(self) -> None:
        """
        Initialize graph model

        Creates an empty graph model including data structures for nodes, edges, and node weights.
        """
        self.nodes: Set[int] = set()                    # Set of nodes
        self.adj_list: List[Set[int]] = []              # Adjacency list
        self.node_weights: Dict[int, float] = {}        # Node weights dictionary
        self._problem_data: Optional[ProblemData] = None # ProblemData object
    
    def add_node(self, node: int) -> None:
        """
        Add a node to the model
        
        Parameters:
            node: ID of the node to add
            
        Note:
            If the node already exists, it will not be added again
        """
        self.nodes.add(node)
        
        # If the node is not in the weight dictionary, add default weight
        if node not in self.node_weights:
            self.node_weights[node] = 0.0
        
        # Adjust adjacency list size
        while len(self.adj_list) <= node:
            self.adj_list.append(set())
    
    def add_node_weight(self, node: int, weight: float) -> None:
        """
        Set node weight
        
        Parameters:
            node: Node ID
            weight: Node weight
            
        Note:
            If the node does not exist, it will be automatically added
        """
        # Ensure the node exists
        if node not in self.nodes:
            self.add_node(node)
            
        self.node_weights[node] = weight
    
    def add_edge(self, u: int, v: int) -> None:
        """
        Add an edge to the model
        
        Parameters:
            u: First endpoint of the edge
            v: Second endpoint of the edge
            
        Note:
            If nodes do not exist, they will be automatically added
        """
        # Ensure nodes exist
        if u not in self.nodes:
            self.add_node(u)
        if v not in self.nodes:
            self.add_node(v)
            
        # Ensure adjacency list is large enough
        while len(self.adj_list) <= max(u, v):
            self.adj_list.append(set())
        
        # Add edge (add both directions for undirected graph)
        self.adj_list[u].add(v)
        self.adj_list[v].add(u)
    
    def get_node_count(self) -> int:
        """
        Get the number of nodes in the graph
        
        Returns:
            Number of nodes in the graph
        """
        return len(self.nodes)
    
    def get_edge_count(self) -> int:
        """
        Get the number of edges in the graph
        
        Returns:
            Number of edges in the graph
        """
        # Calculate total edges (divide by 2 because each edge is counted twice in an undirected graph)
        return sum(len(neighbors) for neighbors in self.adj_list) // 2

    def getProblemData(self) -> 'ProblemData':
        """
        Get the ProblemData object associated with the model

        Returns:
            ProblemData object
        """
        if self._problem_data is None:
            # Create ProblemData if it doesn't exist
            self._problem_data = self._create_problem_data()
        return self._problem_data
    
    @staticmethod
    def from_data(problem_data: 'ProblemData') -> 'Model': # Using new type hint
        """
        Static factory method to create a model from problem data
        
        Parameters:
            problem_data: ProblemData object # Using new type hint
            
        Returns:
            Initialized model instance
        """
        model = Model()
        
        # Save problem data reference for use in solve method
        model._problem_data = problem_data
        
        return model
    
    def _create_problem_data(self) -> 'ProblemData': # Using new type hint
        """
        Create ProblemData object
        
        Convert model data to PyCNP's ProblemData object
        
        Returns:
            Initialized ProblemData object
        """
        # Create new ProblemData object
        max_node_id = max(self.nodes) if self.nodes else 0
        problem_data = ProblemData(max_node_id + 1) # Using new reference
        
        # Add nodes
        for node in self.nodes:
            problem_data.add_node(node)
        
        # Add node weights    
        for node, weight in self.node_weights.items():
            problem_data.add_node_weight(node, weight)
        
        # Add edges (avoid adding duplicates)
        for u in range(len(self.adj_list)):
            for v in self.adj_list[u]:
                if u < v:  # Only add undirected edge once
                    problem_data.add_edge(u, v)
        
        return problem_data
    
    def solve(self, 
              problem_type: str, 
              bound: int, 
              stopping_criterion: StoppingCriterion,
              seed: int=0,
              K: int = 2**30,
              memetic_search_params: Optional[MemeticSearchParams] = None,
              variable_population_params: Optional[VariablePopulationParams] = None,
              display: bool = True,
              collect_stats: bool=True) -> 'Result':
        """
        Solve the graph problem defined by the model
        
        Parameters:
            problem_type: Problem type ("CNP", "DCNP", "NWCNP")
            bound: Number of nodes to remove
            stopping_criterion: Stopping criterion.
            seed: Random seed
            K: K value for DCNP problems (default is infinity)
            memetic_search_params: Parameters for the memetic search.
            variable_population_params: Parameters for variable population size.
            display: Whether to output detailed information
            collect_stats: Whether to collect statistics
            
        Returns:
            Solution result, including optimal solution and objective value
            
        Exceptions:
            RuntimeError: When problem type is unknown or no stopping criterion is set
        """
        
        # Get or create problem data
        if hasattr(self, '_problem_data') and self._problem_data is not None:
            # Use existing problem_data
            problem_data = self._problem_data
        else:
            # Create new ProblemData object from model data
            problem_data = self._create_problem_data()
            # Save the created problem_data for future use
            self._problem_data = problem_data
        
        if memetic_search_params is None:
            memetic_search_params = MemeticSearchParams()

        if variable_population_params is None:
            variable_population_params = VariablePopulationParams()

        ms = MemeticSearch(
            problem_data=problem_data,
            problem_type=problem_type,
            bound=bound,
            seed=seed,
            K=K,
            memetic_search_params=memetic_search_params,
            variable_population_params=variable_population_params,
        )
        
        # Run algorithm to get result
        result = ms.run(
            stopping_criterion=stopping_criterion,
            display=display,
            collect_stats=collect_stats
        )
        return result
      
    
    def __str__(self) -> str:
        """
        Return string representation of the graph model
        
        Returns:
            String describing the graph model, including number of nodes and edges
        """
        return f"Model(nodes: {self.get_node_count()}, edges: {self.get_edge_count()})"
    
    def __repr__(self) -> str:
        """
        Return detailed string representation of the graph model
        
        Returns:
            Detailed string describing the graph model
        """
        return f"Model(nodes: {sorted(list(self.nodes))}, edges: {self.get_edge_count()})"
