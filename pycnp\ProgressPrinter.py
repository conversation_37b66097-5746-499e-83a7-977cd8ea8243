import time
from typing import Optional
from importlib.metadata import version as get_version

from .Result import Result
from .Statistics import Statistics

# Templates for various different outputs.
_ITERATION_TEMPLATE = (
    "{indicator}Iter {iter:>5} | Time: {elapsed:>6.2f}s | Best: {best_obj:<10.2f} | "
    "Idle: {idle:>4} | PopSize: {pop_size:>3}"
)

_START_TEMPLATE = """PyCNP v{version}

Solving an instance with:
    Problem type: {problem_type_name}
    Bound: {bound}
    Seed:  {seed}
"""

_ITERATION_HEADER_TEMPLATE = """
  Iter        | Time        | Best        | Idle    | PopSize
"""

_END_TEMPLATE = """
----------------------------------------------------------------------
MimeticSearch finished.
    Total iterations: {iters}
    Total runtime: {runtime:.2f} seconds
    Best objective found: {best_obj:.2f}
    Nodes in solution: {nodes_in_solution}
    Best solution found: {best_solution}
"""

_POPULATION_EVENT_TEMPLATE = "--- {event_type} --- PopSize: {pop_size}"


class ProgressPrinter:
    """
    Handles printing progress information for MemeticSearch.
    Inspired by PyVRP's ProgressPrinter.
    """

    def __init__(self, should_print: bool):
        """
        Parameters
        ----------
        should_print
            Whether to print information to the console.
        """
        self._print = should_print
        self._last_print_time = time.perf_counter()
        self._print_interval = 1 # Print every second, adjust as needed
        self._last_printed_iter_num = 0 # Track the last iteration number that was actually printed
        self._current_best_obj = float('inf') # Added to track current best objective

    def _should_print_now(self) -> bool:
        """Determines if enough time has passed to print again."""
        return time.perf_counter() - self._last_print_time >= self._print_interval

    def start(self, problem_type_name: str, bound: int, seed: int):
        """
        Outputs information about the search settings.
        """
        if not self._print:
            return
            
        pycnp_version = "unknown"
        try:
            pycnp_version = get_version("pycnp")
        except Exception:
            print("Warning: Could not get pycnp version. Is 'pycnp' installed or package name correct?")
            
        msg = _START_TEMPLATE.format(
            version=pycnp_version,
            problem_type_name=problem_type_name,
            bound=bound,
            seed=seed
        )
        print(msg)
        self._current_best_obj = float('inf') # Reset best objective for the run

    def initializing_population_message(self):
        """
        Prints a message before population initialization starts.
        """
        if not self._print:
            return
        print("\n -----------------------Initializing population-----------------------") # Added newline for spacing
        self._last_print_time = time.perf_counter() # Avoid resetting timer here to not affect iteration print interval 

    def print_iterations_start_banner_and_header(self):
        """
        Prints a message before the main iteration loop starts and the header.
        """
        if not self._print:
            return
        print("--------------------Starting Population Iterations--------------------")
        print(_ITERATION_HEADER_TEMPLATE)
        self._last_print_time = time.perf_counter() # Reset timer after printing header and before iterations start

    def iteration(self, stats: 'Statistics', force_print: bool = False):
        """
        Outputs relevant information based on the latest statistics.
        Prints periodically or if force_print is True.
        """
        if not self._print or not stats.is_collecting() or not stats.data:
            return

        # Print every N seconds or if forced
        if not force_print and not self._should_print_now():
             return

        latest_stats: _IterationStats = stats.data[-1]
        total_runtime = sum(s.runtime for s in stats.data)

        indicator = " "
        if latest_stats.best_obj_value < self._current_best_obj:
            self._current_best_obj = latest_stats.best_obj_value
            indicator = "*"
        
        msg = _ITERATION_TEMPLATE.format(
            indicator=indicator,
            iter=stats.num_iterations,
            elapsed=total_runtime,
            best_obj=latest_stats.best_obj_value,
            idle=latest_stats.num_idle_generations,
            pop_size=latest_stats.population_size,
        )
        print(msg)
        self._last_print_time = time.perf_counter()
        self._last_printed_iter_num = stats.num_iterations # Update when a print actually occurs

    def population_event(self, event_type: str, population_size: int):
        """
        Prints a message for population events like expand or rebuild.
        """
        if not self._print:
            return
            
        msg = _POPULATION_EVENT_TEMPLATE.format(
            event_type=event_type.capitalize(), # e.g., Expand, Rebuild
            pop_size=population_size
        )
        print(msg)
        self._last_print_time = time.perf_counter() # Ensure event messages don't delay iteration prints too much
        
    def end(self, result: 'Result'):
        """
        Outputs information about the search duration and the best-found
        solution.
        """
        if not self._print:
            return
            
        # Force print the last iteration stats if its data hasn't been printed yet.
        if result.stats and result.stats.data and result.stats.num_iterations > self._last_printed_iter_num:
            self.iteration(result.stats, force_print=True)

        msg = _END_TEMPLATE.format(
            iters=result.num_iterations,
            runtime=result.runtime,
            best_obj=result.cost(),
            nodes_in_solution=len(result.best_solution),
            best_solution=str(result.best_solution),
        )
        print(msg)
