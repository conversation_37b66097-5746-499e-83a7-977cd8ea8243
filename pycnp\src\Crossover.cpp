
#include "Crossover.h"
#include "search/Search.h"
#include <chrono>
#include <iostream>
#include <memory>
#include <random>
#include <stdexcept>
#include <unordered_set>


Crossover::Crossover(int seed_val) : seed(seed_val) { rng.setSeed(seed_val); }

std::unique_ptr<Graph> Crossover::doubleBackboneBasedCrossover(
    const Graph& originalGraph, std::pair<Solution const*, Solution const*> const& parents)
{
    const auto& MSolution = *parents.first;   
    const auto& FSolution = *parents.second;  
    
    std::unordered_set<Node> nodesToRemove;
    nodesToRemove.reserve(MSolution.size());
    
    for (Node node : MSolution)
    {
        if (FSolution.contains(node))
        {  
            nodesToRemove.insert(node);
        }
        else if (rng.generateProbability() < THETA)
        {  
            nodesToRemove.insert(node);
        }
    }
    for (Node node : FSolution)
    {
        if (nodesToRemove.contains(node))
            continue;
            
        if (rng.generateProbability() < THETA)
        {
            nodesToRemove.insert(node);
        }
    }

    std::unique_ptr<Graph> offspring = originalGraph.clone();
    offspring->updateGraphByRemovedNodes(nodesToRemove);

    int currentCount = nodesToRemove.size();
    int targetCount = MSolution.size();

    if (currentCount < targetCount)
    {
        for (int i = 0; i < targetCount - currentCount; ++i)
        {
            ComponentIndex componentIndex = offspring->selectRemovedComponent();
            Node nodeToRemove = offspring->randomSelectNodeFromComponent(componentIndex);
            offspring->removeNode(nodeToRemove);
        }
    }
    else if (currentCount > targetCount)
    {
        for (int i = 0; i < currentCount - targetCount; ++i)
        {
        
            Node nodeToAdd = offspring->greedySelectNodeToAdd();
            offspring->addNode(nodeToAdd);
        }
    }

    return offspring;
}

std::unique_ptr<Graph> Crossover::reduceSolveCombine(
    const Graph &originalGraph,
    std::pair<Solution const *, Solution const *> const &parents)
{

    const auto &MSolution = *parents.first;
    const auto &FSolution = *parents.second;

    std::unordered_set<Node> nodesToRemove;
    nodesToRemove.reserve(std::min(MSolution.size(), FSolution.size()));

    for (Node node : MSolution)
    {
        if (FSolution.contains(node) && rng.generateProbability() < BETA)
        {
            nodesToRemove.insert(node);
        }
    }

    std::unique_ptr<Graph> reduceOriginalGraph = originalGraph.clone();
    reduceOriginalGraph->getReducedGraphByRemovedNodes(nodesToRemove);

    std::unique_ptr<Graph> reducedGraph(
        reduceOriginalGraph->getRandomFeasibleGraph());

    Search search(*reducedGraph, seed);
    if (dynamic_cast<const DCNP_Graph *>(&originalGraph))
    {
        search.setStrategy("BCLS");
    }
    else
    {
        search.setStrategy("CHNS");
    }
    SearchResult result = search.run();

    std::unique_ptr<Graph> improvedGraph = originalGraph.clone();

    NodeSet finalNodes = nodesToRemove;
    finalNodes.insert(result.solution.begin(), result.solution.end());

    improvedGraph->updateGraphByRemovedNodes(finalNodes);

    return improvedGraph;
}

std::unique_ptr<Graph> Crossover::inherit_repair_recombination(
    const Graph &originalGraph,
    std::tuple<Solution const *, Solution const *, Solution const *> const
        &parents)
{

    const auto &parent1Nodes = *std::get<0>(parents);
    const auto &parent2Nodes = *std::get<1>(parents);
    const auto &parent3Nodes = *std::get<2>(parents);

    int numToRemove = parent1Nodes.size();

    std::unique_ptr<Graph> offspring = originalGraph.clone();

    int maxNodeId = 0;
    for (const auto &parentNodes : {parent1Nodes, parent2Nodes, parent3Nodes})
    {
        for (Node node : parentNodes)
        {
            maxNodeId = std::max(maxNodeId, node);
        }
    }

    std::vector<int> nodeFrequency(maxNodeId + 1, 0);
    std::unordered_set<Node> nodesToRemove;
    nodesToRemove.reserve(numToRemove);

    for (const auto &parentNodes : {parent1Nodes, parent2Nodes, parent3Nodes})
    {
        for (Node node : parentNodes)
        {
            nodeFrequency[node]++;

            if (nodeFrequency[node] == 3)
            {
                nodesToRemove.insert(node);
            }
        }
    }

    std::vector<Node> freq2Candidates, freq1Candidates, freq0Candidates;
    for (Node node = 0; node <= maxNodeId; node++)
    {

        if (!nodesToRemove.contains(node))
        {
            if (nodeFrequency[node] == 2)
            {
                freq2Candidates.push_back(node);
            }
            else if (nodeFrequency[node] == 1)
            {
                freq1Candidates.push_back(node);
            }
            else if (nodeFrequency[node] == 0)
            {
                freq0Candidates.push_back(node);
            }
        }
    }

    int targetPhase2 = static_cast<int>(TARGET_RATIO * numToRemove);
    while (static_cast<int>(nodesToRemove.size()) < targetPhase2)
    {
        double r = rng.generateProbability();
        Node nodeToRemove = Graph::INVALID_NODE;

        if (r < P2 && !freq2Candidates.empty())
        {

            int idx = rng.generateInt(0, freq2Candidates.size() - 1);
            nodeToRemove = freq2Candidates[idx];
            freq2Candidates.erase(freq2Candidates.begin() + idx);
        }
        else if (r < P2 + (1 - P2) * P1 && !freq1Candidates.empty())
        {

            int idx = rng.generateInt(0, freq1Candidates.size() - 1);
            nodeToRemove = freq1Candidates[idx];
            freq1Candidates.erase(freq1Candidates.begin() + idx);
        }
        else if (!freq0Candidates.empty())
        {

            int idx = rng.generateInt(0, freq0Candidates.size() - 1);
            nodeToRemove = freq0Candidates[idx];
            freq0Candidates.erase(freq0Candidates.begin() + idx);
        }

        if (nodeToRemove != Graph::INVALID_NODE)
        {
            nodesToRemove.insert(nodeToRemove);
        }
    }

    offspring->updateGraphByRemovedNodes(nodesToRemove);

    while (static_cast<int>(nodesToRemove.size()) < numToRemove)
    {

        Node nodeToRemove = offspring->findBestNodeToRemove();
        offspring->removeNode(nodeToRemove);
        nodesToRemove.insert(nodeToRemove);
    }

    return offspring;
}
