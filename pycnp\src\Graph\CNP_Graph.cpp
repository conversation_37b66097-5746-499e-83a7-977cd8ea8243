#include "CNP_Graph.h"

CNP_Graph::CNP_Graph(NodeSet nodes,
                     std::vector<NodeSet> adjList,
                     int bound,
                     int seed)
{
    numNodes = nodes.size();
    originalNodesSet = nodes;
    nodeAge.resize(numNodes, 0);
    currentAdjList = adjList;
    originalAdjList = adjList;
    numToRemove = bound;
    nodeToComponentIndex.resize(numNodes, -1);
    rng.setSeed(seed);
}

void CNP_Graph::initializeComponentsAndMapping()
{
    std::fill(nodeToComponentIndex.begin(), nodeToComponentIndex.end(), -1);

    connectedComponents.clear();
    connectedPairs = 0;

    ComponentIndex componentIndex = 0;
    std::vector<bool> visited(numNodes, false);

    for (Node node : originalNodesSet)
    {
        if (!visited[node] && !isNodeRemoved(node))
        {
            Component component = DFSfindComponent(node);
            if (!component.nodes.empty())
            {
                connectedComponents.push_back(component);

                for (Node componentNode : component.nodes)
                {
                    visited[componentNode] = true;
                    nodeToComponentIndex[componentNode] = componentIndex;
                }
                componentIndex++;

                connectedPairs += (component.size * (component.size - 1)) / 2;
            }
        }
    }
}

Component CNP_Graph::DFSfindComponent(Node startNode) const
{
    Component newComponent;
    newComponent.nodes.reserve(numNodes);

    std::vector<bool> isVisited(numNodes, false);
    std::vector<Node> stack;
    stack.reserve(numNodes);
    stack.push_back(startNode);

    while (!stack.empty())
    {
        Node node = stack.back();
        stack.pop_back();

        if (isVisited[node] || isNodeRemoved(node))
        {
            continue;
        }

        isVisited[node] = true;
        newComponent.nodes.push_back(node);

        const auto &neighbors = currentAdjList[node];
        for (const Node &neighbor : neighbors)
        {
            if (!isVisited[neighbor] && !isNodeRemoved(neighbor))
            {
                stack.push_back(neighbor);
            }
        }
    }

    newComponent.size = newComponent.nodes.size();
    return newComponent;
}

void CNP_Graph::updateGraphByRemovedNodes(const NodeSet &nodesToRemove)
{
    removedNodes.clear();
    currentAdjList = originalAdjList;

    for (Node node : nodesToRemove)
    {
        removedNodes.insert(node);
    }

    for (Node node : nodesToRemove)
    {
        for (Node neighbor : originalAdjList[node])
        {
            currentAdjList[neighbor].erase(node);
        }
        currentAdjList[node].clear();
    }

    initializeComponentsAndMapping();
}

void CNP_Graph::getReducedGraphByRemovedNodes(const NodeSet &removeSet)
{
    removedNodes.clear();
    numToRemove -= removeSet.size();

    for (Node node : removeSet)
    {
        originalNodesSet.erase(node);

        for (Node neighbor : originalAdjList[node])
        {
            originalAdjList[neighbor].erase(node);
        }
        originalAdjList[node].clear();
    }

    currentAdjList = originalAdjList;
    initializeComponentsAndMapping();
}

void CNP_Graph::addNode(Node nodeToAdd)
{
    removedNodes.erase(nodeToAdd);
    ComponentIndex componentIndex = -1;

    for (Node neighbor : originalAdjList[nodeToAdd])
    {
        if (nodeToComponentIndex[neighbor] != -1)
        {
            currentAdjList[nodeToAdd].insert(neighbor);
            currentAdjList[neighbor].insert(nodeToAdd);

            if (componentIndex == -1)
            {
                componentIndex = nodeToComponentIndex[neighbor];
            }
        }
    }

    if (componentIndex != -1)
    {
        connectedComponents[componentIndex].nodes.push_back(nodeToAdd);
        connectedComponents[componentIndex].size++;
        nodeToComponentIndex[nodeToAdd] = componentIndex;

        Component newComponent = DFSfindComponent(nodeToAdd);

        if (newComponent.size > connectedComponents[componentIndex].size)
        {
            connectedComponents[componentIndex].size--;

            std::set<ComponentIndex> componentsIndexesToMerge;
            for (Node node : newComponent.nodes)
            {
                if (nodeToComponentIndex[node] != -1)
                {
                    componentsIndexesToMerge.insert(nodeToComponentIndex[node]);
                }
            }

            std::vector<int> indexMapping(connectedComponents.size());
            std::iota(indexMapping.begin(), indexMapping.end(), 0);

            for (ComponentIndex idx : componentsIndexesToMerge)
            {
                for (size_t i = idx + 1; i < indexMapping.size(); ++i)
                {
                    indexMapping[i]--;
                }
            }

            for (Node node : originalNodesSet)
            {
                if (nodeToComponentIndex[node] != -1)
                {
                    nodeToComponentIndex[node]
                        = indexMapping[nodeToComponentIndex[node]];
                }
            }

            for (auto it = componentsIndexesToMerge.rbegin();
                 it != componentsIndexesToMerge.rend();
                 ++it)
            {
                connectedPairs -= (connectedComponents[*it].size
                                   * (connectedComponents[*it].size - 1))
                                  / 2;
                connectedComponents.erase(connectedComponents.begin() + *it);
            }

            connectedComponents.push_back(newComponent);

            connectedPairs += (newComponent.size * (newComponent.size - 1)) / 2;

            for (Node node : newComponent.nodes)
            {
                nodeToComponentIndex[node] = connectedComponents.size() - 1;
            }
        }
        else if (newComponent.size == connectedComponents[componentIndex].size)
        {
            connectedPairs += (connectedComponents[componentIndex].size - 1);
        }
    }
    else
    {
        Component newComponent;
        newComponent.nodes.push_back(nodeToAdd);
        newComponent.size = 1;

        connectedComponents.push_back(newComponent);

        nodeToComponentIndex[nodeToAdd] = connectedComponents.size() - 1;
    }
}

void CNP_Graph::removeNode(Node nodeToRemove)
{
    ComponentIndex componentIndex = nodeToComponentIndex[nodeToRemove];
    Component originalComponent = connectedComponents[componentIndex];

    removedNodes.insert(nodeToRemove);

    nodeToComponentIndex[nodeToRemove] = -1;

    const auto &neighbors = currentAdjList[nodeToRemove];
    for (Node neighbor : neighbors)
    {
        currentAdjList[neighbor].erase(nodeToRemove);
    }
    currentAdjList[nodeToRemove].clear();

    if (originalComponent.size == 1)
    {
        for (size_t i = componentIndex + 1; i < connectedComponents.size(); i++)
        {
            for (Node node : connectedComponents[i].nodes)
            {
                nodeToComponentIndex[node]--;
            }
        }
        connectedComponents.erase(connectedComponents.begin() + componentIndex);
        return;
    }

    if (originalComponent.size > 1)
    {
        std::erase(connectedComponents[componentIndex].nodes, nodeToRemove);
        connectedComponents[componentIndex].size--;

        std::vector<bool> isVisited(numNodes, false);

        Node startNode = -1;
        for (Node node : originalComponent.nodes)
        {
            if (node != nodeToRemove)
            {
                startNode = node;
                break;
            }
        }

        Component newComponent = DFSfindComponent(startNode);

        if (newComponent.size < connectedComponents[componentIndex].size)
        {
            connectedPairs -= (connectedComponents[componentIndex].size
                               * (connectedComponents[componentIndex].size + 1))
                              / 2;

            connectedComponents[componentIndex] = newComponent;

            connectedPairs += (newComponent.size * (newComponent.size - 1)) / 2;

            for (Node node : newComponent.nodes)
            {
                isVisited[node] = true;
                nodeToComponentIndex[node] = componentIndex;
            }

            for (Node node : originalComponent.nodes)
            {
                if (isVisited[node] || node == nodeToRemove)
                    continue;

                Component splitComponent = DFSfindComponent(node);
                ComponentIndex newIndex = connectedComponents.size();

                connectedComponents.push_back(splitComponent);

                connectedPairs
                    += (splitComponent.size * (splitComponent.size - 1)) / 2;

                for (Node componentNode : splitComponent.nodes)
                {
                    nodeToComponentIndex[componentNode] = newIndex;
                    isVisited[componentNode] = true;
                }
            }
        }
        else
        {
            connectedPairs -= newComponent.size;
        }
    }
}

ComponentIndex CNP_Graph::selectRemovedComponent() const
{
    size_t numComponents = connectedComponents.size();
    std::vector<ComponentIndex> largeComponents;
    largeComponents.reserve(numComponents);

    if (numComponents > 50)
    {
        return selectRemovedLargerComponent();
    }

    int minSize = numNodes;
    int maxSize = 0;

    for (size_t i = 0; i < numComponents; ++i)
    {
        const size_t size = connectedComponents[i].size;
        if (size > 2)
        {
            minSize = std::min(minSize, static_cast<int>(size));
            maxSize = std::max(maxSize, static_cast<int>(size));
        }
    }

    const double sizeThreshold
        = maxSize - (maxSize - minSize) * 0.5 - rng.generateIndex(3);

    for (size_t i = 0; i < numComponents; ++i)
    {
        if (connectedComponents[i].size >= sizeThreshold)
        {
            largeComponents.push_back(i);
        }
    }

    if (largeComponents.empty())
    {
        throw std::runtime_error("can not find suitable large component");
    }

    return largeComponents[rng.generateIndex(largeComponents.size())];
}

ComponentIndex CNP_Graph::selectRemovedLargerComponent() const
{
    size_t totalSize = numNodes - removedNodes.size();
    size_t numComponents = connectedComponents.size();
    size_t avgComponentSize = std::max(
        static_cast<size_t>(2),
        static_cast<size_t>(std::round(static_cast<float>(totalSize)
                                    / static_cast<float>(numComponents))));

    std::vector<ComponentIndex> largeComponents;
    std::vector<size_t> componentSizes;
    largeComponents.reserve(numComponents);
    componentSizes.reserve(numComponents);

    size_t totalNodesInBigComponents = 0;
    size_t maxSize = 0;
    size_t maxIndex = 0;
    size_t secondMaxSize = 0;
    size_t secondMaxIndex = 0;

    for (size_t i = 0; i < numComponents; ++i)
    {
        const size_t currentSize = connectedComponents[i].size;

        if (currentSize > avgComponentSize)
        {
            largeComponents.push_back(i);
            componentSizes.push_back(currentSize);
            totalNodesInBigComponents += currentSize;

            if (currentSize > maxSize)
            {
                secondMaxSize = maxSize;
                secondMaxIndex = maxIndex;
                maxSize = currentSize;
                maxIndex = i;
            }
            else if (currentSize > secondMaxSize)
            {
                secondMaxSize = currentSize;
                secondMaxIndex = i;
            }
        }
    }

    if (largeComponents.size() == 1)
    {
        return rng.generateBool(0.5) ? secondMaxIndex : largeComponents[0];
    }

    const int index = rng.generateIndex(totalNodesInBigComponents);
    int sum = 0;

    for (size_t i = 0; i < largeComponents.size(); ++i)
    {
        sum += componentSizes[i];
        if (index < sum)
        {
            return largeComponents[i];
        }
    }

    return largeComponents.back();
}

Node CNP_Graph::randomSelectNodeFromComponent(
    ComponentIndex componentIndex) const
{
    const auto &component = connectedComponents[componentIndex];
    if (component.nodes.empty())
    {
        throw std::runtime_error("component is empty, can not select node");
    }
    return component.nodes[rng.generateIndex(component.size)];
}

Node CNP_Graph::ageSelectNodeFromComponent(ComponentIndex componentIndex) const
{
    const auto &component = connectedComponents[componentIndex];
    if (component.size == 0)
    {
        throw std::runtime_error("component is empty, can not select node");
    }

    std::vector<Node> candidateNodes;
    candidateNodes.reserve(component.size);

    const Node firstNode = component.nodes[0];
    int minAge = nodeAge[firstNode];
    candidateNodes.push_back(firstNode);

    for (size_t i = 1; i < component.size; ++i)
    {
        Node currentNode = component.nodes[i];
        if (nodeAge[currentNode] < minAge)
        {
            minAge = nodeAge[currentNode];
            candidateNodes.clear();
            candidateNodes.push_back(currentNode);
        }
        else if (nodeAge[currentNode] == minAge)
        {
            candidateNodes.push_back(currentNode);
        }
    }

    return candidateNodes.size() == 1
               ? candidateNodes[0]
               : candidateNodes[rng.generateIndex(candidateNodes.size())];
}

Node CNP_Graph::impactSelectNodeFromComponent(
    ComponentIndex componentIndex) const
{
    const auto &component = connectedComponents[componentIndex];
    if (component.size == 0)
    {
        throw std::runtime_error("component is empty, can not select node");
    }

    std::vector<int> nodeToIdx(numNodes, -1);
    for (size_t i = 0; i < component.size; ++i)
    {
        nodeToIdx[component.nodes[i]] = i;
    }

    const int size = component.size;
    dfn.assign(size + 1, 0);
    lowVec.assign(size + 1, 0);
    stSizeVec.assign(size + 1, 1);
    cutSizeVec.assign(size + 1, 1);
    impactVec.assign(size + 1, 0);
    flagVec.assign(size + 1, 0);
    isCutVec.assign(size + 1, false);

    std::vector<Node> candidateNodes;
    candidateNodes.reserve(size);

    timeStamp = 0;
    nodeRoot = 1;

    tarjanInComponent(componentIndex, nodeRoot, nodeToIdx);

    int minImpact = std::numeric_limits<int>::max();
    candidateNodes.clear();

    for (int i = 1; i <= size; ++i)
    {
        int currentImpact = impactVec[i];

        if (isCutVec[i])
        {
            currentImpact += ((timeStamp - cutSizeVec[i])
                              * (timeStamp - cutSizeVec[i] - 1))
                             / 2;
        }
        else
        {
            currentImpact += ((timeStamp - 1) * (timeStamp - 2)) / 2;
        }

        if (currentImpact < minImpact)
        {
            minImpact = currentImpact;
            candidateNodes.clear();
            candidateNodes.push_back(component.nodes[i - 1]);
        }
        else if (currentImpact == minImpact)
        {
            candidateNodes.push_back(component.nodes[i - 1]);
        }
    }

    return candidateNodes.size() == 1
               ? candidateNodes[0]
               : candidateNodes[rng.generateIndex(candidateNodes.size())];
}

void CNP_Graph::tarjanInComponent(ComponentIndex compIndex,
                                  int nodeIdx,
                                  const std::vector<int> &nodeToIdx) const
{
    dfn[nodeIdx] = lowVec[nodeIdx] = ++timeStamp;

    Node nodeId = connectedComponents[compIndex].nodes[nodeIdx - 1];

    for (Node neighbor : currentAdjList[nodeId])
    {
        if (!isNodeRemoved(neighbor)
            && nodeToComponentIndex[neighbor] == compIndex)
        {
            int neighborIdx = nodeToIdx[neighbor] + 1;

            if (dfn[neighborIdx] == 0)
            {
                tarjanInComponent(compIndex, neighborIdx, nodeToIdx);

                lowVec[nodeIdx]
                    = std::min(lowVec[nodeIdx], lowVec[neighborIdx]);

                if (dfn[nodeIdx] < dfn[neighborIdx])
                {
                    stSizeVec[nodeIdx] += stSizeVec[neighborIdx];
                }

                if (lowVec[neighborIdx] >= dfn[nodeIdx])
                {
                    flagVec[nodeIdx]++;

                    if (nodeIdx != nodeRoot)
                    {
                        isCutVec[nodeIdx] = true;
                        cutSizeVec[nodeIdx] += stSizeVec[neighborIdx];
                        impactVec[nodeIdx] += (stSizeVec[neighborIdx]
                                               * (stSizeVec[neighborIdx] - 1))
                                              / 2;
                    }
                    else if (nodeIdx == nodeRoot && flagVec[nodeIdx] > 1)
                    {
                        isCutVec[nodeIdx] = true;
                    }
                }
            }
            else
            {
                lowVec[nodeIdx] = std::min(lowVec[nodeIdx], dfn[neighborIdx]);
            }
        }
    }
}

Node CNP_Graph::greedySelectNodeToAdd() const
{
    if (removedNodes.empty())
    {
        throw std::runtime_error("no removed nodes can be added");
    }

    std::vector<Node> candidateNodes;
    candidateNodes.reserve(removedNodes.size());

    auto it = removedNodes.begin();
    const Node firstNode = *it;
    int minDelta = calculateConnectionGain(firstNode);
    candidateNodes.push_back(firstNode);

    ++it;
    for (; it != removedNodes.end(); ++it)
    {
        Node currentNode = *it;
        int connectionGain = calculateConnectionGain(currentNode);

        if (connectionGain < minDelta)
        {
            minDelta = connectionGain;
            candidateNodes.clear();
            candidateNodes.push_back(currentNode);
        }
        else if (connectionGain == minDelta)
        {
            candidateNodes.push_back(currentNode);
        }
    }

    return candidateNodes.size() == 1
               ? candidateNodes[0]
               : candidateNodes[rng.generateIndex(candidateNodes.size())];
}

Node CNP_Graph::randomSelectNodeToRemove() const
{
    ComponentIndex compIndex = rng.generateIndex(connectedComponents.size());
    const Component &selectedComponent = connectedComponents[compIndex];

    if (selectedComponent.size == 0 || selectedComponent.nodes.empty())
    {
        throw std::runtime_error("selected component is empty, can not select node");
    }

    return selectedComponent.nodes[rng.generateIndex(selectedComponent.size)];
}

int CNP_Graph::calculateConnectionGain(Node node) const
{
    std::vector<size_t> componentSizes(connectedComponents.size(), 0);

    int totalSize = 1;

    const auto &neighbors = originalAdjList[node];
    for (Node neighbor : neighbors)
    {
        if (nodeToComponentIndex[neighbor] != -1)
        {
            ComponentIndex compIndex = nodeToComponentIndex[neighbor];

            if (componentSizes[compIndex] == 0)
            {
                componentSizes[compIndex] = connectedComponents[compIndex].size;
                totalSize += componentSizes[compIndex];
            }
        }
    }

    int oldConnectionsSum = 0;
    for (int size : componentSizes)
    {
        if (size > 0)
        {
            oldConnectionsSum += (size * (size - 1)) / 2;
        }
    }

    int newConnections = (totalSize * (totalSize - 1)) / 2;

    return newConnections - oldConnectionsSum;
}

std::unique_ptr<Graph> CNP_Graph::getRandomFeasibleGraph()
{
    auto tempGraph = std::make_unique<CNP_Graph>(*this);
    Solution randomSolution;
    std::vector<Node> availableNodes(originalNodesSet.begin(),
                                     originalNodesSet.end());

    for (int i = 0; i < numToRemove && !availableNodes.empty(); ++i)
    {
        int randomIndex = rng.generateIndex(availableNodes.size());
        Node node = availableNodes[randomIndex];

        randomSolution.insert(node);

        availableNodes[randomIndex] = availableNodes.back();
        availableNodes.pop_back();
    }

    tempGraph->updateGraphByRemovedNodes(randomSolution);
    return tempGraph;
}

std::unique_ptr<Graph> CNP_Graph::clone() const
{
    return std::make_unique<CNP_Graph>(*this);
}
