#include "DLASStrategy.h"
#include "Graph/CNP_Graph.h"
#include "Graph/NWCNP_Graph.h"
#include "SearchResult.h"
#include <algorithm>
#include <numeric>
#include <vector>

DLASStrategy::DLASStrategy(
    Graph &graph, const std::unordered_map<std::string, std::any> &params)
    : graph(graph), params(params)
{

    auto it = params.find("maxIdleSteps");
    if (it != params.end())
    {
        try
        {
            maxIdleSteps = std::any_cast<int>(it->second);
        }
        catch (const std::bad_any_cast &)
        {
        }
    }

    it = params.find("historyLength");
    if (it != params.end())
    {
        try
        {
            historyLength = std::any_cast<int>(it->second);
        }
        catch (const std::bad_any_cast &)
        {
        }
    }

    it = params.find("seed");
    if (it != params.end())
    {
        try
        {
            int seed = std::any_cast<int>(it->second);

            if (seed > 0)
            {
                rng.setSeed(seed);
            }
        }
        catch (const std::bad_any_cast &)
        {
        }
    }
}

SearchResult DLASStrategy::execute()
{

    SearchResult result;

    Graph &currentGraph = graph;
    NodeSet bestSolution;

    if (auto *cnpGraph = dynamic_cast<CNP_Graph *>(&currentGraph))
    {
        bestSolution = cnpGraph->removedNodes;
    }
    else if (auto *nwcnpGraph = dynamic_cast<NWCNP_Graph *>(&currentGraph))
    {
        bestSolution = nwcnpGraph->removedNodes;
    }

    int currentObjValue = graph.getObjectiveValue();
    int bestObjValue = currentObjValue;
    long numSteps = 0;
    long numIdleSteps = 0;

    std::vector<int> historyCost(historyLength, currentObjValue);
    int maxCost = currentObjValue;
    int numMaxCost = historyLength;

    while (numIdleSteps < maxIdleSteps)
    {
        numSteps++;

        performMove(currentGraph,
                    currentObjValue,
                    historyCost,
                    maxCost,
                    numMaxCost,
                    numSteps);

        if (currentObjValue < bestObjValue)
        {
            bestSolution = currentGraph.getRemovedNodes(); // 现在返回const引用，会自动拷贝
            bestObjValue = currentObjValue;
            numIdleSteps = 0;
        }
        else
        {
            numIdleSteps++;
        }
    }

    result.solution = bestSolution;
    result.objValue = bestObjValue;
    return result;
}

void DLASStrategy::performMove(Graph &currentGraph,
                               int &currentObjValue,
                               std::vector<int> &historyCost,
                               int &maxCost,
                               int &numMaxCost,
                               int numSteps)
{

    NodeSet previousRemovedNodes = currentGraph.getRemovedNodes(); // 保存当前状态的拷贝
    int previousObjValue = currentObjValue;

    ComponentIndex componentToRemove = currentGraph.selectRemovedComponent();

    Node nodeToRemove
        = currentGraph.randomSelectNodeFromComponent(componentToRemove);

    currentGraph.removeNode(nodeToRemove);

    if (auto *nwcnpGraph = dynamic_cast<NWCNP_Graph *>(&currentGraph))
    {

        while (nwcnpGraph->SolWeight > nwcnpGraph->bound)
        {

            Node nodeToAdd = currentGraph.greedySelectNodeToAdd();

            currentGraph.addNode(nodeToAdd);

            currentGraph.setNodeAge(nodeToAdd, numSteps);
        }
    }
    else
    {

        Node nodeToAdd = currentGraph.greedySelectNodeToAdd();

        if (nodeToAdd != Graph::INVALID_NODE)
        {
            currentGraph.addNode(nodeToAdd);
            currentGraph.setNodeAge(nodeToAdd, numSteps);
        }
    }

    currentObjValue = currentGraph.getObjectiveValue();

    int historyLength = historyCost.size();
    int historyIndex = numSteps % historyLength;

    if (currentObjValue == previousObjValue || currentObjValue < maxCost)
    {
    }
    else
    {

        currentGraph.updateGraphByRemovedNodes(previousRemovedNodes);
        currentObjValue = previousObjValue;
    }

    if (currentObjValue > historyCost[historyIndex])
    {

        historyCost[historyIndex] = currentObjValue;
    }
    else if (currentObjValue < historyCost[historyIndex]
             && currentObjValue < previousObjValue)
    {

        historyCost[historyIndex] = currentObjValue;

        if (historyCost[historyIndex] == maxCost)
        {
            numMaxCost--;
        }

        if (numMaxCost == 0)
        {

            maxCost = *std::max_element(historyCost.begin(), historyCost.end());

            numMaxCost
                = std::count(historyCost.begin(), historyCost.end(), maxCost);
        }
    }
}