#pragma once
#include "Crossover.h"
#include "RandomNumberGenerator.h"
#include "search/Search.h"
#include <chrono>
#include <functional>
#include <memory>
#include <vector>
class Graph;

/**
 * VariablePopulationParams(
 *     max_pop_size: int = 20,
 *     increase_pop_size: int = 3,
 *     max_idle_gens: int = 20
 * )
 *
 * Parameter configuration for the memetic search within the :class:`~Population`.
 *
 * Attributes
 * ----------
 * maxPopSize
 *     Maximum population size.
 * increasePopSize
 *     Number of individuals to add when expanding the population.
 * maxIdleGens
 *     Number of idle generations after which the population might be updated or rebuilt.
 */
struct VPParams
{
    int maxPopSize = 20;
    int increasePopSize = 3;
    int maxIdleGens = 20;

    VPParams() = default;
    VPParams(int max_pop, int inc_pop, int idle_gens)
        : maxPopSize(max_pop), increasePopSize(inc_pop), maxIdleGens(idle_gens) {}
};

/**
 * @brief Parameters for the memetic search.
 *
 * This struct holds parameters that control the behavior of the memetic search algorithm,
 * specifically related to the population initialization.
 *
 * @param initial_pop_size The initial size of the population.
 * @param is_pop_variable A flag indicating whether the population size can vary.
 */
struct MSParams
{
    std::string search = "CHNS";
    std::string crossover = "RSC";
    bool is_pop_variable = true;
    int initial_pop_size = 5;

    MSParams() = default;
    MSParams(std::string search_str, std::string crossover_str, bool is_pop_var, int init_pop_size)
        : search(search_str), crossover(crossover_str), is_pop_variable(is_pop_var), initial_pop_size(init_pop_size) {}
};

/**
 * The Population class is responsible for managing the population of solutions
 * in a genetic or memetic algorithm. It handles operations such as
 * initialization, updates, selection, expansion, and rebuilding of the
 * population.
 */
class Population
{
private:
    /**
     * Item
     *
     * 种群中个体的表示
     *
     * 代表种群中的一个个体，包含解、目标函数值、适应度和相似性信息。
     * 支持移动语义以提高性能。
     *
     * Attributes
     * ----------
     * solution
     *     个体的解表示（如移除的节点集合）
     * objValue
     *     个体解的目标函数值
     * fitness
     *     个体的适应度值，可能结合目标值和多样性
     * similarity
     *     与其他解的相似性分数列表，用于多样性计算
     */
    struct Item
    {
        Solution solution;
        int objValue;
        double fitness;
        using Similarity = std::vector<std::pair<double, Solution>>;
        Similarity similarity;

        Item(Solution sol, int obj, double fit)
            : solution(std::move(sol)), objValue(obj), fitness(fit)
        {
            similarity.reserve(20); 
        }

        Item(const Item&) = default;
        Item(Item&&) = default;
        Item& operator=(const Item&) = default;
        Item& operator=(Item&&) = default;
    };

    Graph &originalGraph;                     ///< Reference to the original graph.
    VPParams variablePopulationParams;  ///< Parameters for the memetic search.
    std::vector<Item> population;             ///< The current population of individuals.
    RandomNumberGenerator *rng;               ///< Random number generator.
    int initPopSize;                          ///< Initial population size.
    bool isVariablePopulation;                ///< Whether the population size is variable.
    std::string search;                       ///< Search strategy.

    static constexpr double ALPHA = 0.60;  ///< Weight to balance cost and diversity in fitness calculation.

    /**
     * Updates the fitness values of all individuals in the population.
     * Fitness is typically a combination of the objective value and diversity.
     */
    void updateFitness();

    /**
     * Removes the worst solution from the population, usually based on fitness.
     */
    void removeWorstSolution();

    /** 
     * Computes the similarity between two solutions.
     * This implementation uses the Jaccard similarity coefficient.
     *
     * Parameters
     * ----------
     * sol1
     *     The first solution.
     * sol2
     *     The second solution.
     *
     * Returns
     * -------
     * double
     *     The Jaccard similarity score between the two solutions.
     */
    double computeSimilarity(const Solution &sol1, const Solution &sol2) const
    {
        // Calculate Jaccard similarity between two solutions
        int intersection = 0;
        for (auto node : sol1)
        {
            if (sol2.find(node) != sol2.end())
            {
                intersection++;
            }
        }
        return static_cast<double>(intersection)
            / (sol1.size() + sol2.size() - intersection);
    }

public:
    /**
     * Population(originalGraph: Graph&, params: const VariablePopulationParams&, seed: int)
     *
     * Constructs a Population instance.
     *
     * Parameters
     * ----------
     * originalGraph
     *     A reference to the original graph problem.
     * initPopSize
     *     Initial population size.
     * variablePopulationParams
     *     Parameters for the variable population.
     * seed
     *     Seed for the random number generator.
     */
    Population(Graph &originalGraph,
               const MSParams &memetic_search_params,
               const VPParams &params,
               int seed)
        : originalGraph(originalGraph),
          variablePopulationParams(params),
          rng(new RandomNumberGenerator())
    {
        rng->setSeed(seed);
        initPopSize = memetic_search_params.initial_pop_size;
        isVariablePopulation = memetic_search_params.is_pop_variable;
        search = memetic_search_params.search;
    }

    ~Population() { delete rng; }

    
    /** @brief Test documentation for initialize. */
    std::pair<Solution, int>
    initialize(bool display = false,
            std::function<bool(int)> stopping_criterion = nullptr);

    /**
     * Updates the population with a new solution, potentially triggering
     * population management mechanisms like culling or expansion based on
     * idle generations.
     *
     * Parameters
     * ----------
     * solution
     *     The new solution to consider for the population.
     * objValue
     *     The objective value of the new solution.
     * verbose
     *     Whether to print detailed information during the update process.
     */
    void update(const Solution &solution,
                int objValue,
                int num_idle_generations,
                bool verbose = false);

    /**
     * Adds an individual (solution and its objective value) to the population.
     * This method might trigger population sizing logic if the maximum
     * population size is exceeded.
     *
     * Parameters
     * ----------
     * solution
     *     The solution to add.
     * objValue
     *     The objective value of the solution.
     */
    void add(const Solution &solution, int objValue);

    /**
     * Selects two solutions from the population using tournament selection.
     *
     * Parameters
     * ----------
     * k
     *     The size of the tournament (number of individuals randomly chosen
     *     for competition). Defaults to 2.
     *
     * Returns
     * -------
     * std::pair<Solution, Solution>
     *     A pair containing the two selected solutions.
     */
    std::pair<Solution, Solution> tournamentSelectTwoSolutions(int k = 2);

    /**
     * Retrieves up to three prominent solutions from the population.
     * These typically include the best, second best, and a diverse solution.
     * If the population size is less than three, it returns the available solutions.
     *
     * Returns
     * -------
     * std::tuple<Solution, Solution, Solution>
     *     A tuple containing three solutions. If fewer than three solutions exist,
     *     some parts of the tuple might be default-constructed or placeholder solutions.
     */
    std::tuple<Solution, Solution, Solution> getAllThreeSolutions() const;

    /**
     * Gets the best individual (Item) currently in the population,
     * usually based on fitness or objective value.
     *
     * Returns
     * -------
     * const Item&
     *     A constant reference to the best item in the population.
     */
    const Item &getBestItem() const;

    /**
     * Checks if a given solution is already present in the population.
     *
     * Parameters
     * ----------
     * solution
     *     The solution to check for duplication.
     *
     * Returns
     * -------
     * bool
     *     True if the solution is a duplicate, false otherwise.
     */
    bool isDuplicate(const Solution &solution) const;

    /**
     * Gets the current size of the population.
     *
     * Returns
     * -------
     * size_t
     *     The number of individuals in the population.
     */
    size_t getSize() const { return population.size(); }

    /**
     * Generates a new solution that is not a duplicate of any solution
     * currently in the population.
     *
     * Returns
     * -------
     * std::pair<Solution, int>
     *     A pair containing the newly generated non-duplicate solution and its objective value.
     */
    std::pair<Solution, int> generateNonDuplicateSolution();

    /**
     * Expands the population by adding new, diverse individuals.
     * This is typically called when the population needs more diversity or
     * to explore new areas of the search space.
     */
    void expand();

    /**
     * Rebuilds the population, often to introduce diversity or escape
     * local optima. This might involve generating a new set of individuals
     * while potentially keeping some elite solutions from the current population.
     */
    void rebuild();
};
