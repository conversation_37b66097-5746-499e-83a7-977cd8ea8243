A brief introduction to the MA
==========================================================

PyCNP provides a high-performance implementation of the Memetic Algorithm (MA) for critical node problems (CNPs). MA combines population-based approaches with neighborhood-based local search. This hybrid approach allows for effective exploration and exploitation of the search space, leading to high-quality solutions.


.. note::

   For a more thorough introduction to MA for CNPs, we refer to the papers by <PERSON><PERSON> et al. (2019) <https://ieeexplore.ieee.org/document/8403891>`_ .


The MA algorithm which is implemented in `PyCNP` works as follows. MA maintains a population of solutions, in every iteration of the search loop, the algorithm selects existing parent solutions from the population. Then a crossover operator is used to generate an offspring solution that inherits features from selected parents. After the crossover, the offspring solution is further improved using a search procedure. The resulting candidate solution will be added to the population if it is not a duplicate, and the least fit solution is removed from the population to keep the population size constant.

If the algorithm has not found a better solution for a certain number of iterations, the population size will be adjusted: if the population size is less than the maximum size, the algorithm will add new solutions to the population to expand the population; if the population size is greater than the maximum size, the algorithm will remove solutions from the population to reduce the population size.The algorithm continues until a provided stopping criterion is met, at which point it returns the best solution found. In pseudocode, MA works as follows:

| **Input:** the initial population size
| **Output:** the best-found solution :math:`s^*`
| Set :math:`s^*` to the initial solution with the best objective value
| **repeat** until stopping criterion is met:
|     Select parent solutions :math:`(s^{p_1}, s^{p_2})` from the population using :math:`k`-ary tournament.
|     Apply crossover operator to generate an offspring solution :math:`s^o=XO(s^{p_1}, s^{p_2})`.
|     Improve the offspring using a search procedure :math:`LS` to obtain :math:`s^c=LS(s^o)`.
|     Add the candidate solution to the population.
|     **if** :math:`s^c` has a better objective value than :math:`s^*`:
|         :math:`s^* \gets s^c`
|         :math:`idleiter \gets 0`
|     **else**: 
|         :math:`idleiter \gets idleiter + 1`
|     **if** :math:`idleiter` is greater than a certain threshold:
|         **if** population size < maximum size:
|             Add new solutions to expand the population
|         **else if** population size > maximum size:
|             Remove solutions with lowest fitness to reduce the population
| **return** :math:`s^*`

PyCNP provides multiple crossover operators, stopping criteria, and various search operators. So the MA in PyCNP is highly configurable, allowing you to specify whether the population size is variable、set the initial population size、crossover operator、local search strategy,stopping criteria. You can also use the default algorithm parameters, which generally achieve good results in most cases.
