# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj
# 忽略origincode文件夹
origincode/
# extern/
Instances/

# Precompiled Headers
*.gch
*.pch
.idea/
.vscode/
# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.mod
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Python
__pycache__/
*.py[cod]
*$py.class
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# C++
*.o
*.a
*.so
*.dylib
*.dll
*.exe
*.out
*.app

# CMake
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
Makefile
*.ninja
.ninja_deps
.ninja_log
build/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
results/
*.log
*.csv

# Build artifacts
*.egg-info/
*.so # 忽略编译产生的 .so 文件，除非它们是最终分发包的一部分
*.pyd

# Virtual environment
.env/
env/
venv/
.venv/
