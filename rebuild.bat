@echo off
REM Simple PyCNP rebuild script for Windows
REM This version avoids special characters that might cause encoding issues

echo [INFO] PyCNP Windows Build Script
echo =================================

echo [INFO] Cleaning old build files...
if exist build rmdir /s /q build
if exist pycnp.egg-info rmdir /s /q pycnp.egg-info
if exist dist rmdir /s /q dist
if exist pycnp\_pycnp.pyd del /q pycnp\_pycnp.pyd
if exist pycnp\_pycnp.so del /q pycnp\_pycnp.so

echo [INFO] Installing build dependencies...
pip install -q meson-python pybind11 meson ninja
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install build dependencies
    pause
    exit /b 1
)

echo [INFO] Installing project dependencies...
pip install -q "numpy>=1.19.0"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install project dependencies
    pause
    exit /b 1
)

echo [INFO] Configuring Meson build...
meson setup build --reconfigure
if %errorlevel% neq 0 (
    echo [ERROR] Meson setup failed
    echo [TIP] Make sure you have Visual Studio Build Tools installed
    pause
    exit /b 1
)

echo [INFO] Compiling C++ extensions...
meson compile -C build
if %errorlevel% neq 0 (
    echo [ERROR] Compilation failed
    pause
    exit /b 1
)

echo [INFO] Finding and copying compiled module...
setlocal enabledelayedexpansion
set "found=0"

for /r build %%f in (_pycnp*.pyd) do (
    echo [SUCCESS] Found compiled module: %%f
    copy "%%f" "pycnp\_pycnp.pyd" >nul
    set "found=1"
    goto :test_import
)

for /r build %%f in (_pycnp*.so) do (
    echo [SUCCESS] Found compiled module: %%f
    copy "%%f" "pycnp\_pycnp.so" >nul
    set "found=1"
    goto :test_import
)

if !found! equ 0 (
    echo [ERROR] Compiled module file not found
    pause
    exit /b 1
)

:test_import
echo [INFO] Testing module import...
python -c "import sys; sys.path.insert(0, '.'); import pycnp; print('[SUCCESS] PyCNP module imported successfully!')"
if %errorlevel% neq 0 (
    echo [ERROR] Module import test failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Build completed successfully!
echo.
echo [USAGE] Usage Instructions:
echo   * Run example: python examples\model-CNP.py
echo   * Or run: python examples\model-DCNP.py
echo   * Test installation: python run_examples_windows.py
echo.
echo [TIP] For PyCharm: Open project and set project root as source root
echo.

pause
