
#include "DCNP_Graph.h"

DCNP_Graph::DCNP_Graph(NodeSet nodes,
                    int K,
                    std::vector<NodeSet> adjList,
                    int numToRemove,
                    int seed)
{
    this->numNodes = nodes.size();
    this->kHops = K;
    this->originalNodesSet = nodes;
    this->originalAdjList = adjList;
    this->currentAdjList = adjList;
    this->numToRemove = numToRemove;
    this->rng.setSeed(seed);

    nodeAge.resize(numNodes, 0);

    intree.resize(numNodes, std::vector<int>(numNodes, 0));

    treeSize.resize(numNodes, 0);

    build_tree();
}

void DCNP_Graph::bfs_k_tree(Node v)
{

    std::fill(intree[v].begin(), intree[v].end(), 0);

    if (isNodeRemoved(v))
    {
        treeSize[v] = 0;
        return;
    }

    int fast = 0, slow = 0;

    std::vector<bool> isVisited(numNodes, false);
    std::vector<int> level(numNodes, 0);
    std::vector<Node> visitList(numNodes);

    visitList[slow] = v;
    isVisited[v] = true;

    do
    {
        Node currentNode = visitList[slow];

        if (level[currentNode] < kHops)
        {

            for (Node neighbor : currentAdjList[currentNode])
            {

                if (!isNodeRemoved(neighbor) && !isVisited[neighbor])
                {
                    visitList[++fast] = neighbor;
                    isVisited[neighbor] = true;
                    level[neighbor] = level[currentNode] + 1;
                }
            }
        }

        intree[v][currentNode] = 1;
        slow++;
    } while (slow <= fast);

    treeSize[v] = slow - 1;
}

void DCNP_Graph::build_tree()
{
    for (int i = 0; i < numNodes; i++)
    {
        bfs_k_tree(i);
    }
}

void DCNP_Graph::updateGraphByRemovedNodes(const NodeSet &nodesToRemove)
{

    removedNodes.clear();

    currentAdjList = originalAdjList;

    for (Node node : nodesToRemove)
    {
        removedNodes.insert(node);
    }

    build_tree();
}

void DCNP_Graph::getReducedGraphByRemovedNodes(const NodeSet &removeSet)
{

    removedNodes.clear();

    numToRemove -= removeSet.size();

    for (Node node : removeSet)
    {

        originalNodesSet.erase(node);

        for (Node neighbor : currentAdjList[node])
        {
            originalAdjList[neighbor].erase(node);
        }

        originalAdjList[node].clear();
    }

    currentAdjList = originalAdjList;

    build_tree();
}

void DCNP_Graph::addNode(Node nodeToAdd)
{

    removedNodes.erase(nodeToAdd);

    bfs_k_tree(nodeToAdd);

    for (int i = 0; i < numNodes; i++)
    {

        if (intree[nodeToAdd][i])
        {
            bfs_k_tree(i);
        }
    }
}

void DCNP_Graph::removeNode(Node nodeToRemove)
{

    removedNodes.insert(nodeToRemove);

    for (int i = 0; i < numNodes; i++)
    {

        if (intree[i][nodeToRemove])
        {
            bfs_k_tree(i);
        }
    }
}

const std::vector<double> &DCNP_Graph::calculateBetweennessCentrality() const
{

    static std::vector<double> betweenness;
    betweenness.resize(numNodes, 0.0);

    for (int s = 0; s < numNodes; s++)
    {
        if (isNodeRemoved(s))
            continue;

        std::stack<int> S;

        std::vector<std::list<int>> P(numNodes);

        std::vector<int> d(numNodes, -1);

        std::vector<int> sigma(numNodes, 0);

        sigma[s] = 1;
        d[s] = 0;

        std::queue<int> Q;
        Q.push(s);

        while (!Q.empty())
        {
            int v = Q.front();
            Q.pop();

            S.push(v);

            for (Node w : currentAdjList[v])
            {

                if (isNodeRemoved(w))
                    continue;

                if (d[w] < 0)
                {
                    Q.push(w);
                    d[w] = d[v] + 1;
                }

                if (d[w] == d[v] + 1)
                {

                    sigma[w] += sigma[v];

                    P[w].push_back(v);
                }
            }
        }

        std::vector<double> delta(numNodes, 0.0);

        while (!S.empty())
        {
            int w = S.top();
            S.pop();

            for (auto v : P[w])
            {

                delta[v] += (static_cast<double>(sigma[v]) / sigma[w])
                            * (1.0 + delta[w]);
            }

            if (w != s)
            {
                betweenness[w] += delta[w];
            }
        }
    }

    return betweenness;
}

int DCNP_Graph::calculateKHopTreeSize() const
{
    int sum = 0;

    for (int i = 0; i < numNodes; i++)
    {
        if (!isNodeRemoved(i))
        {
            sum += treeSize[i];
        }
    }

    return sum / 2;
}

std::unique_ptr<Graph> DCNP_Graph::getRandomFeasibleGraph()
{
    auto tempGraph = std::make_unique<DCNP_Graph>(*this);

    NodeSet nodesToRemove;

    std::vector<Node> availableNodes(originalNodesSet.begin(),
                                    originalNodesSet.end());

    for (int i = 0; i < numToRemove && !availableNodes.empty(); i++)
    {

        int randomIndex = rng.generateIndex(availableNodes.size());
        nodesToRemove.insert(availableNodes[randomIndex]);

        availableNodes[randomIndex] = availableNodes.back();
        availableNodes.pop_back();
    }

    tempGraph->updateGraphByRemovedNodes(nodesToRemove);

    return tempGraph;
}

Node DCNP_Graph::findBestNodeToRemove()
{

    int currentObjective = calculateKHopTreeSize();
    Node bestNode = INVALID_NODE;
    std::vector<Node> bestList;
    int maxImprovement = 0;

    for (int i = 0; i < numNodes; i++)
    {
        if (!isNodeRemoved(i))
        {

            removeNode(i);
            int newObjective = calculateKHopTreeSize();

            int improvement = currentObjective - newObjective;

            if (improvement > maxImprovement)
            {
                maxImprovement = improvement;
                bestNode = i;
                bestList.clear();
                bestList.push_back(bestNode);
            }

            else if (improvement == maxImprovement)
            {
                bestList.push_back(i);
            }

            addNode(i);
        }
    }

    if (bestList.size() > 1)
    {
        bestNode = bestList[rng.generateIndex(bestList.size())];
    }

    return bestNode;
}

Node DCNP_Graph::findBestNodeToAdd()
{

    NodeSet solution = getRemovedNodes();
    int currentObjective = calculateKHopTreeSize();
    Node bestNode = INVALID_NODE;
    std::vector<Node> bestList;
    int minDeterioration = std::numeric_limits<int>::max();

    for (Node node : solution)
    {

        addNode(node);
        int newObjective = calculateKHopTreeSize();

        int deterioration = newObjective - currentObjective;

        if (deterioration < minDeterioration)
        {
            minDeterioration = deterioration;
            bestNode = node;
            bestList.clear();
            bestList.push_back(bestNode);
        }

        else if (deterioration == minDeterioration)
        {
            bestList.push_back(node);
        }

        removeNode(node);
    }

    if (bestList.size() > 1)
    {
        bestNode = bestList[rng.generateIndex(bestList.size())];
    }

    return bestNode;
}

Node DCNP_Graph::randomSelectNodeToRemove() const
{

    int randomIndex = rng.generateIndex(numNodes);
    while (isNodeRemoved(randomIndex))
    {
        randomIndex = rng.generateIndex(numNodes);
    }
    return randomIndex;
}

std::unique_ptr<Graph> DCNP_Graph::clone() const
{
    return std::make_unique<DCNP_Graph>(*this);
}
