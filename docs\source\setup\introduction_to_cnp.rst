A brief introduction to the CNP
===============================================

Given an undirected graph :math:`G = (V, E)` with vertex (or node) set :math:`V` and edge set :math:`E`, critical node detection problems (CNDPs) aim to identify a subset of nodes (referred to as *critical nodes*) :math:`S \subseteq V` whose removal enhances (decreases) the graph connectivity of the residual graph :math:`G[V \setminus S]` evaluated by a given connectivity measure :math:`\sigma`. 

According to different cases of :math:`|S|` and :math:`\sigma`, CNDPs can be divided into two categories: :math:`K`-vertex-CNDP and :math:`\beta`-connectivity-CNDP. The former is to optimize (minimize or maximize) the connectivity measure :math:`\sigma`, such that no more than :math:`K` nodes are deleted (i.e., :math:`|S| \leq K`), whereas the latter aims to minimize the set of deleted nodes, such that the connectivity measure :math:`\sigma` is bounded by a given threshold :math:`\beta` 

.. note::

   For a more thorough introduction to the taxonomy of CNDPs, we refer to the papers by <PERSON><PERSON> et al. (2021) <https://ieeexplore.ieee.org/document/9149656>`_.

The critical node problem (CNP) is a fundamental CNDP, which belongs to the category of :math:`K`-vertex-CNDPs. It seeks a set :math:`S \subseteq V` of at most :math:`K` nodes, the deletion of which minimizes the total pairwise connectivity in :math:`G[V \setminus S]`. Formally, the objective function :math:`f(S)` of CNP can be written as follows:

.. math::
   f(S) = \sum_{i=1}^{M} \frac{|C_i|(|C_i| - 1)}{2},

where :math:`C_i` is a connected component, and :math:`M` is the total number of connected components in the residual graph :math:`G[V \setminus S]`. Hence, the residual graph :math:`G[V \setminus S]` is composed of :math:`M` connected components, that is, :math:`\sum_{i=1}^{M} \cup C_i = G[V \setminus S]`. 

A good solution of CNP should generate a residual graph that maximizes the number of connected components while simultaneously minimizing the variance in the component sizes. And the CNP has been shown to be NP-hard and has attracted widespread research attention.

Supported CNP variants
----------------------------------


Node-Weighted Critical Node Problem
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

NWCNP is a node-weighted version of CNP, which has the same objective function as CNP and simultaneously satisfies the following budgetary constraint:

.. math::
    \sum_{i=1}^{|S|} w(v_{S(i)}) \leq K,

where the terms :math:`w(v_{S(i)}) > 0` are positive weights associated with each node, and :math:`K > 0` is a predefined budget limit.

CNP can be considered as a special case of NWCNP where the weight of each node is set to one, that is, :math:`w(v_i) = 1`, :math:`\forall v_i \in V`. 

Distance-based Critical Node Problem
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

DCNP aims to remove a subset of nodes of cardinality at most :math:`B` to minimize a distance-based connectivity metric.The DCNP supported by PyCNP is the general case, which has the same constraint as CNP but the objective function is minimizing the number of node pairs connected by a path of length at most :math:`k`. The objective function can be described as follows:

.. math::
   \min_{S \subseteq V} \sum_{i,j \in V \setminus S, i<j} \psi(d(i, j)) 

where :math:`\psi(d(i,j))` is defined as follows:

.. math::
   \psi(d(i,j)) = 
   \begin{cases} 
   1, & d(i,j) \leq k, \\
   0, & \text{otherwise},
   \end{cases} 

- :math:`d(i, j)` is the distance (i.e., the length of the shortest path) between nodes :math:`i` and :math:`j` in the residual graph :math:`G[V \setminus S]`. For any two disconnected nodes :math:`i` and :math:`j` in :math:`G[V \setminus S]`, we have :math:`d(i, j) = +\infty`.
- :math:`k` is a given positive integer indicating a maximal distance limit

It means that only when the hop distance between a pair of nodes does not exceed :math:`k`, they can be considered as a truly connected node pair.

Special Cases:

- :math:`k = 1`, DCNP reduces to minimizing the number of remaining edges, which is also known as the maximum coverage problem (MCP). The goal of MCP is to find a subset :math:`S \subseteq V` with a fixed number of nodes and the number of edges covered by :math:`S` is maximized, which is equivalent to minimizing the number of remaining edges.
- :math:`k \geq n - 1`, DCNP reduces to minimizing the total number of connected node pairs, which is the classic CNP.

.. note::

   For a more thorough introduction to the Distance-based Critical Node Problem, we refer to the papers by `Zhou et al. (2023) <https://www.sciencedirect.com/science/article/abs/pii/S0377221722008967?via%3Dihub>`_.