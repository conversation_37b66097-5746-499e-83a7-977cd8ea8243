"""
MemeticSearch module

Provides Python implementation of memetic search algorithms for solving critical node problems.
Contains parameter configuration classes and main search algorithm implementations.
"""

import time
import random
from typing import Optional, Union
from dataclasses import dataclass, field

from pycnp.stop import StoppingCriterion
from pycnp.Result import Result
from pycnp.Statistics import Statistics
from pycnp.ProgressPrinter import ProgressPrinter
from ._pycnp import (
    CNP, DCNP, NWCNP,           # Problem type constants
    CBNS, CHNS, DLAS, BCLS,     # Search strategy constants
    DBX, RSC, IRR,              # Crossover strategy constants
    Population, Search, Crossover, ProblemData, VPParams, MSParams
)

# Valid crossover strategy constants for parameter validation (supports both strings and constants)
VALID_CROSSOVER_STRATEGIES = {"DBX", "RSC", "IRR", DBX, RSC, IRR}

@dataclass
class MemeticSearchParams:
    """
    Memetic search parameter configuration class

    Used to configure various parameters of the MemeticSearch algorithm. Implemented using dataclass
    to maintain interface compatibility with the original C++ version.

    Attributes
    ----------
    search : Union[int, str], default=CHNS
        Search strategy, can be integer constant or string name
    crossover : Union[str, object], default="RSC"
        Crossover strategy, can use strings or constants:

        - "DBX" or DBX: Double Backbone crossover operator
          * Only applicable to CNP problem type
          * Does not support DCNP and NWCNP problem types
        - "RSC" or RSC: Reduce-Solve-Combine crossover operator
          * Applicable to all problem types (CNP, DCNP, NWCNP)
          * Recommended general crossover strategy
        - "IRR" or IRR: Inherit-Repair-Recombination crossover operator
          * Only applicable to DCNP problem type
          * Requires initial_pop_size=3
          * Requires is_pop_variable=False (does not support variable population)
    is_pop_variable : bool, default=True
        Whether population size is variable
    initial_pop_size : int, default=5
        Initial population size

    Raises
    ------
    ValueError
        If the provided crossover strategy is invalid
    """
    search: Union[int, str] = field(default=CHNS)
    crossover: Union[str, object] = "RSC"
    is_pop_variable: bool = True
    initial_pop_size: int = 5

    def __post_init__(self):
        """Validate parameter validity"""
        # Validate crossover parameter
        if self.crossover not in VALID_CROSSOVER_STRATEGIES:
            raise ValueError(f"Invalid crossover strategy: {self.crossover}. "
                            f"Valid options are: 'DBX'/'RSC'/'IRR' or DBX/RSC/IRR constants")

        # Convert constants to strings for subsequent processing
        if self.crossover == DBX:
            self.crossover = "DBX"
        elif self.crossover == RSC:
            self.crossover = "RSC"
        elif self.crossover == IRR:
            self.crossover = "IRR"


@dataclass
class VariablePopulationParams:
    """
    Parameter configuration for the variable population size mechanism.

    Attributes:
        max_pop_size: Maximum population size (default: 20).
        increase_pop_size: Number of individuals to add when expanding the population (default: 3).
        max_idle_gens: Number of idle generations after which the population might be updated or rebuilt (default: 20).
    """
    max_pop_size: int = 20
    increase_pop_size: int = 3
    max_idle_gens: int = 20


class MemeticSearch:
    """
    Multi-population search algorithm implementation
    
    An evolutionary algorithm framework combining population management, crossover operations, 
    and local search for solving CNP/DCNP/NWCNP problems
    """
    
    def __init__(self, problem_data: 'ProblemData', 
                problem_type: Union[str, int],
                bound: int,
                seed: int,
                K: int = 2**30, # K value for DCNP problems, default is infinity
                memetic_search_params: MemeticSearchParams = MemeticSearchParams(),
                variable_population_params: VariablePopulationParams = VariablePopulationParams()
                ):
        
        """
        Initialize MemeticSearch algorithm
        
        Parameters:
            problem_data: Problem data reference
            problem_type: Problem type ("CNP", "DCNP", "NWCNP") or PyCNP constant
            bound: Number of nodes to remove
            seed: Random seed
            K: K value for DCNP problems, default is infinity
            memetic_search_params: MemeticSearchParams object, used to configure the MemeticSearch algorithm
            variable_population_params: VariablePopulationParams object, used to configure the variable population size
        """
        self.problem_data = problem_data
        
        # --- Inlined problem_type conversion ---
        if isinstance(problem_type, str):
            if problem_type == "CNP":
                self.problem_type = CNP
                self.problem_type_name = "CNP"
            elif problem_type == "DCNP":
                self.problem_type = DCNP
                self.problem_type_name = "DCNP"
            elif problem_type == "NWCNP":
                self.problem_type = NWCNP
                self.problem_type_name = "NWCNP"
            else:
                raise ValueError(f"Unsupported problem type string: {problem_type}. Valid options are: CNP, DCNP, NWCNP")
        elif problem_type in (CNP, DCNP, NWCNP):
            self.problem_type = problem_type
            # Determine the name for the given integer problem_type
            if problem_type == CNP:
                self.problem_type_name = "CNP"
            elif problem_type == DCNP:
                self.problem_type_name = "DCNP"
            elif problem_type == NWCNP:
                self.problem_type_name = "NWCNP"
            else:
                self.problem_type_name = "Unknown Problem Type"
        else:
            raise ValueError(f"Unsupported problem type: {problem_type}. Must be one of CNP, DCNP, NWCNP or corresponding constants.")
        # --- End inlined problem_type conversion ---
        
        self.bound = bound
        self.K = K    

        # --- BEGIN: Re-add search handling logic ---
        # Handle search (consistent with original MemeticSearchParams.__post_init__ logic)
        if isinstance(memetic_search_params.search, str):
            if memetic_search_params.search == "CBNS":
                self.search_strategy = CBNS
            elif memetic_search_params.search == "CHNS":
                self.search_strategy = CHNS
            elif memetic_search_params.search == "DLAS":
                self.search_strategy = DLAS
            elif memetic_search_params.search == "BCLS":
                self.search_strategy = BCLS
            else:
                raise ValueError(f"Unsupported search strategy string: {memetic_search_params.search}. Valid options are: CBNS, CHNS, DLAS, BCLS")
        elif memetic_search_params.search in (CBNS, CHNS, DLAS, BCLS):
            self.search_strategy = memetic_search_params.search
        else:
            raise ValueError(f"Invalid search strategy value: {memetic_search_params.search}. Must be one of CBNS, CHNS, DLAS, BCLS or corresponding constants.")

        self.seed = seed
        self.memetic_search_params = memetic_search_params
        self.variable_population_params = variable_population_params        
        random.seed(seed)
        
        # Check compatibility between problem type and search strategy
        if self.problem_type == DCNP:
            if self.search_strategy != BCLS: # Use self.search_strategy directly
                raise ValueError("DCNP problem type currently only supports BCLS search strategy")
        elif self.problem_type in [CNP, NWCNP]:
            if self.search_strategy == BCLS: # Use self.search_strategy directly
                raise ValueError("CNP and NWCNP problem types do not support BCLS search strategy")

        # Check compatibility between crossover strategy and problem type
        if self.memetic_search_params.crossover == "DB":
            # DB crossover strategy only applies to CNP problems
            if self.problem_type != CNP:
                raise ValueError(
                    f"DB (Double Backbone crossover) strategy only supports CNP problem type, "
                    f"but got {self.problem_type_name}. "
                    f"For {self.problem_type_name} problems, please use 'RSC' crossover strategy."
                )

        elif self.memetic_search_params.crossover == "IRR":
            # IRR crossover strategy only applies to DCNP problems
            if self.problem_type != DCNP:
                raise ValueError(
                    f"IRR (Inherit-Repair-Recombination crossover) strategy only supports DCNP problem type, "
                    f"but got {self.problem_type_name}. "
                    f"For {self.problem_type_name} problems, please use 'RSC' or 'DB' crossover strategy."
                )

            # IRR crossover strategy requires population size to be exactly 3
            if self.memetic_search_params.initial_pop_size != 3:
                raise ValueError(
                    f"IRR (Inherit-Repair-Recombination crossover) strategy requires exactly 3 individuals in population, "
                    f"but got initial_pop_size={self.memetic_search_params.initial_pop_size}. "
                    f"Please set initial_pop_size=3 when using IRR crossover."
                )

            # IRR crossover strategy does not support variable population
            if self.memetic_search_params.is_pop_variable:
                raise ValueError(
                    f"IRR (Inherit-Repair-Recombination crossover) strategy does not support variable population size. "
                    f"Please set is_pop_variable=False when using IRR crossover."
                )
        
        # Record best solution information
        self.best_solution = set()
        self.best_obj_value = float('inf')
        
        try:
            self.original_graph = self.problem_data.create_original_graph(
                self.problem_type, self.bound, self.seed, self.K
            )
        except Exception as e:
            raise RuntimeError(f"Failed to create original graph: {str(e)}")
        
        self.population: Optional[Population] = None # Initialized in run
    
    def run(self, 
            stopping_criterion: StoppingCriterion,
            collect_stats: bool = True, 
            display: bool = False) -> 'Result':
        """
        Run the algorithm
        
        Parameters:
            stopping_criterion: Stopping criterion
            collect_stats: Whether to collect statistics
            display: Whether to display progress information
            
        Returns:
            Algorithm result object, containing the best solution and objective value, 
            iteration count, and runtime
            
        Exceptions:
            RuntimeError: When no stopping criterion is set
        """
        
        start_time_run = time.perf_counter() 
        
        stats = Statistics(collect_stats=collect_stats)
        printer = ProgressPrinter(should_print=display)
        
        printer.start(self.problem_type_name, self.bound, self.seed)
        
        printer.initializing_population_message()
        
        # Create a C++ MemeticSearchParams object from the Python dataclass
        cpp_memetic_search_params = MSParams(
            search=self.memetic_search_params.search,
            crossover=self.memetic_search_params.crossover,
            is_pop_variable=self.memetic_search_params.is_pop_variable,
            initial_pop_size=self.memetic_search_params.initial_pop_size
        )

        # Create a C++ VariablePopulationParams object from the Python dataclass
        cpp_variable_population_params = VPParams(
            max_pop_size=self.variable_population_params.max_pop_size,
            increase_pop_size=self.variable_population_params.increase_pop_size,
            max_idle_gens=self.variable_population_params.max_idle_gens
        )
        
        self.population = Population(
            self.original_graph,
            cpp_memetic_search_params,
            cpp_variable_population_params,
            self.seed
        )
        
        init_stopping_criterion = None
        if stopping_criterion.get_name() == "MaxRuntime":
            init_stopping_criterion = stopping_criterion
    
        # initialize the population, and it will return the best solution and the best objective value
        best_solution_init, best_obj_value_init = self.population.initialize(
            display,
            init_stopping_criterion)
        
        # update the best solution and the best objective value
        if best_obj_value_init < self.best_obj_value:
            self.best_obj_value = best_obj_value_init
            self.best_solution = best_solution_init
        
        if stopping_criterion.get_name() == "MaxRuntime":
            if not stopping_criterion(self.best_obj_value):
                printer.print_iterations_start_banner_and_header()
        else:
            printer.print_iterations_start_banner_and_header()
            
        num_idle_generations = 0
        iterations = 0
        
        # Main loop
        while not stopping_criterion(self.best_obj_value):
            iterations += 1
            # --- Inlined apply_crossover ---
            crossover = Crossover(self.seed)
            self.seed += 1

            # 根据crossover参数选择交叉算子
            if self.memetic_search_params.crossover == "RSC":
                parent1, parent2 = self.population.select()
                parent_ptrs = [parent1, parent2]
                offspring_graph = crossover.reduce_solve_combine(self.original_graph, parent_ptrs)
            elif self.memetic_search_params.crossover == "DBX":
                parent1, parent2 = self.population.select()
                parent_ptrs = [parent1, parent2]
                offspring_graph = crossover.double_backbone_based_crossover(self.original_graph, parent_ptrs)
            elif self.memetic_search_params.crossover == "IRR":
                parent1, parent2, parent3 = self.population.get_all_three_solutions()
                parent_ptrs = [parent1, parent2, parent3]
                offspring_graph = crossover.inherit_repair_recombination(self.original_graph, parent_ptrs)
            else:
                raise ValueError(f"Unknown crossover strategy: {self.memetic_search_params.crossover}")
            
            # --- Inlined apply_local_search ---
            local_search = Search(offspring_graph, self.seed) # Renamed to avoid conflict
            self.seed += 1
            local_search.set_strategy(self.search_strategy) 
            ls_result = local_search.run()
        
            # --- Inlined apply_population_update ---
            self.population.update(ls_result.solution, ls_result.obj_value, num_idle_generations, display)
        
            
            # Update best solution
            if ls_result.obj_value < self.best_obj_value:
                self.best_solution = ls_result.solution
                self.best_obj_value = ls_result.obj_value
                num_idle_generations = 0
            else:
                num_idle_generations += 1
            
            # Collect statistics for this iteration
            stats.collect(
                best_obj_value=self.best_obj_value, 
                population_size=self.population.get_size(), 
                num_idle_generations=num_idle_generations
            )
            
            # Print progress using printer
            printer.iteration(stats) # Printer handles frequency
        
        # Create final Result object
        final_runtime = time.perf_counter() - start_time_run
        result = Result(
            best_solution=self.best_solution, 
            best_obj_value=self.best_obj_value,
            num_iterations=iterations,
            runtime=final_runtime,
            stats=stats # Pass collected statistics
        )
        
        # Print end info
        printer.end(result)
        
        return result
