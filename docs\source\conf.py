# 导入必要的库
import datetime
import importlib
import inspect
import os
import shutil
import subprocess
import sys

import tomli

# -- 项目信息配置
sys.path.insert(0, os.path.abspath("../../"))

now = datetime.date.today()

project = "PyCNP"  # 项目名称
authors = "PyCNP contributors"  # 作者
repo_url = "https://github.com/xuebo100/PyCNP/"  # 代码仓库URL
copyright = f"2025 - {now.year}, {authors}"  # 版权信息

# 从pyproject.toml文件读取版本信息
with open("../../pyproject.toml", "rb") as fh:
    pyproj = tomli.load(fh)
    release = version = pyproj["tool"]["poetry"]["version"]

# 复制示例笔记本到文档源目录
print("Copying example notebooks into docs/source/examples/")
shutil.copytree("../../examples", "examples/", dirs_exist_ok=True)

# -- API文档配置
autoclass_content = "class"  # 类文档内容
autodoc_member_order = "bysource"  # 成员排序方式
autodoc_typehints = "signature"  # 类型提示显示方式
autodoc_preserve_defaults = True  # 保留默认值

# -- sphinx.ext.linkcode扩展配置
def linkcode_resolve(domain: str, info: dict) -> str | None:
    """
    生成指向PyCNP仓库中指定对象源代码的URL。

    参数
    ----------
    domain: str
        对象的域（例如，"py"表示Python，"cpp"表示C++）。
    info: dict
        包含键"module"和"fullname"的字典。"module"包含
        模块名称作为字符串，"fullname"包含完整对象
        名称作为字符串。

    返回
    -------
    str | None
        指向PyCNP仓库中已识别对象源代码的URL，如果对象
        可以被识别。
    """
    if domain != "py" or not info.get("module") or not info.get("fullname"):
        return None

    obj = importlib.import_module(info["module"])
    for attr_name in info["fullname"].split("."):
        obj = getattr(obj, attr_name)

    try:
        source = inspect.getsourcefile(obj)
    except TypeError:
        return None

    if source is None:
        # 这是一个原生扩展，我们目前无法处理
        return None

    rel_path = source[source.rfind("pycnp/") :]
    line_num = inspect.getsourcelines(obj)[1]

    cmd = "git rev-parse --short HEAD".split()
    revision = subprocess.check_output(cmd).strip().decode("ascii")

    return f"{repo_url}/blob/{revision}/{rel_path}#L{line_num}"

# -- numpydoc扩展配置
numpydoc_xref_param_type = True  # 参数类型交叉引用
numpydoc_class_members_toctree = False  # 类成员不在目录中显示
numpydoc_attributes_as_param_list = False  # 属性不作为参数列表显示
napoleon_include_special_with_doc = True  # 包含带文档的特殊方法

# -- nbsphinx扩展配置（Jupyter笔记本）
skip_notebooks = os.getenv("SKIP_NOTEBOOKS", False)
nbsphinx_execute = "never" if skip_notebooks else "always"  # 是否执行笔记本

# -- 通用配置
extensions = [  # 启用的Sphinx扩展
    "sphinx.ext.duration",
    "sphinx.ext.doctest",
    "sphinx.ext.autodoc",
    "sphinx.ext.intersphinx",
    # "sphinx.ext.linkcode",  # 暂时禁用，避免构建错误
    "sphinx.ext.napoleon",
    "sphinx_immaterial",
    "nbsphinx",
]

exclude_patterns = ["_build", "**.ipynb_checkpoints"]  # 排除的文件模式

# 交叉引用映射配置
intersphinx_mapping = {
    "python": ("https://docs.python.org/3/", None),
    "sphinx": ("https://www.sphinx-doc.org/en/master/", None),
    "numpy": ("https://numpy.org/doc/stable/", None),
    "matplotlib": ("https://matplotlib.org/stable/", None),
}
intersphinx_disabled_domains = ["std"]  # 禁用的交叉引用域

templates_path = ["_templates"]  # 模板路径

add_module_names = False  # 不添加模块名称
python_use_unqualified_type_names = True  # 使用非限定类型名称

# -- HTML输出选项
html_theme = "sphinx_immaterial"  # 使用的HTML主题
html_logo = "assets/images/icon.svg"  # 网站logo
html_theme_options = {  # 主题选项
    "site_url": "https://pycnp.org/",  # 网站URL
    "repo_url": repo_url,  # 仓库URL
    "icon": {  # 图标配置
        "repo": "fontawesome/brands/github",
        "edit": "material/file-edit-outline",
    },
    "features": [  # 启用的功能
        "navigation.expand",
        "navigation.top",
    ],
    "palette": [  # 颜色配置
        {
            "media": "(prefers-color-scheme: light)",  # 浅色模式
            "primary": "blue",
            "accent": "cyan",
            "scheme": "default",
            "toggle": {
                "icon": "material/lightbulb-outline",
                "name": "Switch to dark mode",
            },
        },
        {
            "media": "(prefers-color-scheme: dark)",  # 深色模式
            "primary": "blue",
            "accent": "cyan",
            "scheme": "slate",
            "toggle": {
                "icon": "material/lightbulb",
                "name": "Switch to light mode",
            },
        },
    ],
    "version_dropdown": True,  # 启用版本下拉菜单
    "version_info": [  # 版本信息
        {
            "version": "",
            "title": "Development",
            "aliases": [],
        },
    ],
    "font": False,  # 禁用 Google Fonts
}


# Python类型注解处理配置
python_resolve_unqualified_typing = True  # 解析非限定typing引用
python_transform_type_annotations_pep585 = True  # 转换PEP 585类型注解
python_transform_type_annotations_pep604 = True  # 转换PEP 604类型注解
object_description_options = [  # 对象描述选项
    ("py:.*", dict(include_fields_in_toc=False, include_rubrics_in_toc=False)),
    ("py:attribute", dict(include_in_toc=False)),
    ("py:parameter", dict(include_in_toc=False)),
]


# -- EPUB输出选项
epub_show_urls = "footnote"  # 在脚注中显示URL
