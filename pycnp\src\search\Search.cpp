#include "Search.h"
#include <algorithm>
#include <chrono>
#include <ctime>
#include <iostream>
#include <list>
#include <random>
#include <stdexcept>

Search::Search(Graph &graph, int seed) : graph(graph), seed(seed)
{
    registerStrategies();
}

Search::~Search() = default;

void Search::registerStrategies()
{

    strategyFactory["CBNS"]
        = [](Graph &g, const std::unordered_map<std::string, std::any> &params)
    { return std::make_unique<CBNSStrategy>(g, params); };

    strategyFactory["DLAS"]
        = [](Graph &g, const std::unordered_map<std::string, std::any> &params)
    { return std::make_unique<DLASStrategy>(g, params); };

    strategyFactory["CHNS"]
        = [](Graph &g, const std::unordered_map<std::string, std::any> &params)
    { return std::make_unique<CHNSStrategy>(g, params); };

    strategyFactory["BCLS"]
        = [](Graph &g, const std::unordered_map<std::string, std::any> &params)
    { return std::make_unique<BCLSStrategy>(g, params); };
}

void Search::setStrategy(const std::string &strategyName)
{
    auto it = strategyFactory.find(strategyName);
    if (it != strategyFactory.end())
    {

        auto paramsWithSeed = params;
        paramsWithSeed["seed"] = seed;
        strategy = it->second(graph, paramsWithSeed);
    }
    else
    {
        throw std::invalid_argument("unknown search strategy: " + strategyName);
    }
}

SearchResult Search::run()
{
    if (!strategy)
    {
        throw std::runtime_error("search strategy is not set");
    }
    return strategy->execute();
}

