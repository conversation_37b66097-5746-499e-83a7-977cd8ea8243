Usage Examples
==============

This page provides basic usage examples for PyCNP to help you get started quickly.

Basic Usage
-----------

**Import Required Modules**

.. code-block:: python

   from pycnp import Model, read
   from pycnp.stop import MaxRuntime, MaxIterations

**Load Graph Data**

.. code-block:: python

   # Load graph data from file
   problem_data = read("path/to/your/graph.txt")

   # Or manually build graph data
   from pycnp import ProblemData
   
   data = ProblemData()
   data.add_node(0)
   data.add_node(1)
   data.add_node(2)
   data.add_edge(0, 1)
   data.add_edge(1, 2)

**Create and Solve Model**

.. code-block:: python

   # Create model
   model = Model.from_data(problem_data)

   # Solve CNP problem
   result = model.solve(
       problem_type="CNP",
       bound=5,  # Remove at most 5 nodes
       stopping_criterion=MaxRuntime(30)  # Run for 30 seconds
   )

   # View results
   print(f"Best objective value: {result.best_obj_value}")
   print(f"Removed nodes: {result.best_solution}")

不同问题类型
------------

**CNP (Critical Node Problem)**

.. code-block:: python

   result = model.solve(
       problem_type="CNP",
       bound=10,
       stopping_criterion=MaxRuntime(60)
   )

**NWCNP (Node-Weighted CNP)**

.. code-block:: python

   # 需要先加载节点权重
   problem_data.read_node_weights_from_file("weights.txt")
   
   result = model.solve(
       problem_type="NWCNP",
       bound=15.5,  # 权重预算
       stopping_criterion=MaxRuntime(60)
   )

**DCNP (Distance-based CNP)**

.. code-block:: python

   result = model.solve(
       problem_type="DCNP",
       bound=8,
       k_hop=2,  # 考虑 2-hop 距离
       stopping_criterion=MaxRuntime(60)
   )

高级配置
--------

**自定义算法参数**

.. code-block:: python

   from pycnp import MemeticSearchParams
   
   params = MemeticSearchParams()
   params.initial_population_size = 50
   params.crossover = "IRR"  # 使用继承修复重组交叉
   
   result = model.solve(
       problem_type="CNP",
       bound=10,
       params=params,
       stopping_criterion=MaxRuntime(120)
   )

**组合停止准则**

.. code-block:: python

   from pycnp.stop import NoImprovement
   
   # 使用多个停止准则
   result = model.solve(
       problem_type="CNP",
       bound=10,
       stopping_criterion=[
           MaxRuntime(300),      # 最多运行 5 分钟
           NoImprovement(50)     # 50 次迭代无改进则停止
       ]
   )

完整示例
--------

以下是一个完整的示例程序：

.. literalinclude:: example-.py
   :language: python
   :caption: 完整的 PyCNP 使用示例

更多信息
--------

- 详细的 API 文档请参考 :doc:`../api/pycnp`
- 问题和解答请参考 :doc:`../setup/faq`
- 更多示例请参考 :doc:`index`
