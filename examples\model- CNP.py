from pycnp import Model, read
from pycnp.graph_visualization import visualize_graph
from pycnp.stop import MaxRuntime
from pycnp.MemeticSearch import MemeticSearchParams
from math import floor

# Method 1: Directly read data using read function
problem_data = read("ErdosRenyi_n941.txt")
model = Model.from_data(problem_data)

# Set problem type to DCNP, where the sum of nodes in the solution is not greater than 5% of the total number of nodes
problem_type = "CNP"
bound = 140

# Set hop distance to 3

seed = 4
stopping_criterion = MaxRuntime(5) #
memetic_params = MemeticSearchParams(search="CHNS", crossover="RSC", is_pop_variable=True, initial_pop_size=5)

result = model.solve(
    problem_type=problem_type,
    bound=bound,
    stopping_criterion=stopping_criterion,
    seed=seed,
    memetic_search_params=memetic_params,
    display=True
)

# visualize_graph(
#     problem_data=problem_data,
#     critical_nodes_set=result.best_solution,
# )
# # path = export_graph_vector(model.getProblemData(), result.best_solution, show_after_removal=True,format="pdf")