.. module:: pycnp._pycnp
   :synopsis: Local search algorithms and neighborhood strategies for critical node problems

Local Search Algorithms
========================

The :class:`~pycnp._pycnp.Search` class provides a comprehensive interface for executing
various local search algorithms designed specifically for Critical Node Problems (CNP) and
their variants. It implements a strategy pattern architecture, enabling flexible integration
of different neighborhood search heuristics and optimization strategies.

The search algorithms are designed to improve solution quality through systematic exploration
of solution neighborhoods, making them essential components of hybrid metaheuristic approaches.

.. autoclass:: pycnp._pycnp.Search
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

Search Strategy Constants
-------------------------

PyCNP provides several specialized search strategies, each optimized for different graph
structures and problem characteristics:

.. data:: pycnp.CBNS
   :type: str
   :value: "CBNS"

   **Component-Based Neighborhood Search (CBNS)**

   Focuses on nodes that are critical for maintaining connectivity between components.
   This strategy analyzes the component structure of the graph and prioritizes nodes
   whose removal would most effectively fragment the network.

   **Best for:** Graphs with clear community structure or modular organization.

.. data:: pycnp.CHNS
   :type: str
   :value: "CHNS"

   **Critical Hit Neighborhood Search (CHNS)**

   Targets high-impact nodes with significant centrality measures or connectivity properties.
   This strategy identifies nodes that serve as critical hubs or bridges in the network.

   **Best for:** Scale-free networks, social networks, or graphs with hub-like structures.

.. data:: pycnp.DLAS
   :type: str
   :value: "DLAS"

   **Diversified Local Adaptive Search (DLAS)**

   Implements adaptive neighborhood exploration with dynamic parameter adjustment based
   on search progress. This strategy balances intensification and diversification
   throughout the search process.

   **Best for:** Complex optimization landscapes requiring adaptive search behavior.

.. data:: pycnp.BCLS
   :type: str
   :value: "BCLS"

   **Best Component Local Search (BCLS)**

   Combines component analysis with local optimization within the largest connected
   components. This strategy focuses computational effort on the most promising
   regions of the solution space.

   **Best for:** Large-scale graphs where focusing on major components is beneficial.


Core Search Methods
-------------------

Strategy Configuration
~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pycnp._pycnp.Search.setStrategy

   Configures the search strategy to be used during optimization.

   **Parameters:**

   - ``strategy`` (str): One of the predefined strategy constants (CBNS, CHNS, DLAS, BCLS)

   **Example:**

   .. code-block:: python

      search = Search(graph, seed=42)
      search.setStrategy(pycnp.CBNS)  # Use Component-Based Neighborhood Search

Parameter Configuration
~~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pycnp._pycnp.Search.setParam

   Sets algorithm-specific parameters for fine-tuning search behavior.

   **Parameters:**

   - ``name`` (str): Parameter name
   - ``value``: Parameter value (type depends on the specific parameter)

   **Common Parameters:**

   - ``"max_iterations"``: Maximum number of search iterations
   - ``"improvement_threshold"``: Minimum improvement threshold for continuation
   - ``"neighborhood_size"``: Size of the neighborhood to explore
   - ``"diversification_rate"``: Rate of diversification moves

   .. note::

      This method uses C++ templates for type safety. The Python binding automatically
      handles type conversion for common parameter types (int, double, bool, string).

Search Execution
~~~~~~~~~~~~~~~~

.. automethod:: pycnp._pycnp.Search.run

   Executes the configured search algorithm on the current solution.

   **Returns:**

   - ``SearchResult``: Object containing the improved solution and performance metrics

   **Example:**

   .. code-block:: python

      # Configure and run search
      search.setStrategy(pycnp.DLAS)
      search.setParam("max_iterations", 1000)
      result = search.run(initial_solution)

Usage Examples
--------------

Basic Local Search
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import pycnp
   from pycnp._pycnp import Search

   # Load problem and create graph
   problem_data = pycnp.read("graph.txt")
   graph = problem_data.create_graph("CNP")

   # Create search instance
   search = Search(graph, seed=42)

   # Configure Component-Based Neighborhood Search
   search.setStrategy(pycnp.CBNS)
   search.setParam("max_iterations", 500)
   search.setParam("improvement_threshold", 0.01)

   # Run search on initial solution
   initial_solution = {1, 3, 5, 7, 9}  # Set of nodes to remove
   result = search.run(initial_solution)

   print(f"Improved objective: {result.objective_value}")
   print(f"Improved solution: {result.solution}")

Advanced Multi-Strategy Search
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Test multiple search strategies
   strategies = [pycnp.CBNS, pycnp.CHNS, pycnp.DLAS, pycnp.BCLS]
   best_result = None
   best_objective = float('inf')

   for strategy in strategies:
       search = Search(graph, seed=42)
       search.setStrategy(strategy)

       # Strategy-specific parameter tuning
       if strategy == pycnp.DLAS:
           search.setParam("diversification_rate", 0.1)
           search.setParam("max_iterations", 1000)
       elif strategy == pycnp.CBNS:
           search.setParam("neighborhood_size", 20)
           search.setParam("max_iterations", 500)

       result = search.run(initial_solution)

       if result.objective_value < best_objective:
           best_objective = result.objective_value
           best_result = result
           best_strategy = strategy

   print(f"Best strategy: {best_strategy}")
   print(f"Best objective: {best_objective}")

Integration with Memetic Algorithm
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pycnp import MemeticSearch, MSParams

   # Configure memetic algorithm with local search
   params = MSParams()
   params.population_size = 50
   params.local_search_strategy = pycnp.DLAS
   params.local_search_frequency = 10  # Apply local search every 10 generations

   # Create and run memetic algorithm
   memetic = MemeticSearch(graph, params, seed=42)
   result = memetic.run(max_runtime=300)  # 5 minutes

Performance Considerations
--------------------------

**Computational Complexity**

- **CBNS**: O(|V| + |E|) per iteration - efficient for most graph types
- **CHNS**: O(|V|²) per iteration - higher cost but better quality for hub networks
- **DLAS**: O(|V| log |V|) per iteration - adaptive complexity based on search progress
- **BCLS**: O(|V| + |E|) per iteration - similar to CBNS with component focus

**Memory Usage**

All search strategies use memory-efficient data structures:

- Incremental connectivity computation
- Sparse graph representations
- Efficient neighborhood caching

**Scalability Guidelines**

- **Small graphs** (<100 nodes): All strategies perform well
- **Medium graphs** (100-1000 nodes): CBNS and BCLS recommended
- **Large graphs** (>1000 nodes): CBNS with limited iterations

.. tip::

   For best results, combine multiple search strategies in a sequential or adaptive
   manner. Start with CBNS for broad exploration, then use CHNS for refinement
   on hub-like structures.