import argparse
import os
import sys
from pathlib import Path

# Try to import docblock, provide a helpful error message if it's not found.
try:
    import docblock
except ImportError:
    print(
        "Error: The 'docblock' library is not installed. "
        "Please install it using 'pip install docblock'",
        file=sys.stderr,
    )
    sys.exit(1)

_PREFIX = """\\
#ifndef PYCNP_AUTOGEN_DOCS_H
#define PYCNP_AUTOGEN_DOCS_H

// This file is automatically generated by extract_docstrings.py.
// Do not edit this file manually.
//
// The D() macro is a helper to access the docstrings by their components,
// e.g. D(MyClass) or D(MyClass, my_method).
// It constructs the variable name like PYCNP_doc_MyClass_my_method.

#define PYCNP_CONCAT_IMPL(A, B) A##_##B
#define PYCNP_CONCAT(A, B) PYCNP_CONCAT_IMPL(A, B)

#define PYCNP_GET_D_MACRO(_1, _2, _3, _4, _5, NAME, ...) NAME

#define D_1(p1) PYCNP_doc_##p1
#define D_2(p1, p2) PYCNP_CONCAT(D_1(p1), p2)
#define D_3(p1, p2, p3) PYCNP_CONCAT(D_2(p1, p2), p3)
#define D_4(p1, p2, p3, p4) PYCNP_CONCAT(D_3(p1, p2, p3), p4)
#define D_5(p1, p2, p3, p4, p5) PYCNP_CONCAT(D_4(p1, p2, p3, p4), p5)

#define D(...) PYCNP_GET_D_MACRO(__VA_ARGS__, D_5, D_4, D_3, D_2, D_1)(__VA_ARGS__)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-variable"
#endif

"""

_SUFFIX = """\\

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic pop
#endif

#endif // PYCNP_AUTOGEN_DOCS_H
"""


def to_cpp_stmt(name: str, docstrings: list[str]) -> str:
    """
    Converts a given name and its docstring (extracted by docblock)
    into a C++ static const char* variable definition.
    The variable name is constructed as PYCNP_doc_Part1_Part2_etc based on
    the '::' separated parts of the input 'name'.
    """
    if not docstrings or not docstrings[0].strip():
        # If docstring is empty, don't generate a variable for it
        return ""

    # Sanitize name: replace '::' with '_' for C++ variable name
    # Ensure constructor/destructor names like 'MyClass::~MyClass' are handled.
    # For now, docblock might give 'MyClass' for class, 'MyClass::MyClass' for constructor,
    # 'MyClass::~MyClass' for destructor, 'MyClass::method' for method.
    # Replace '::' with '_', and potentially sanitize other characters if necessary.
    # For example, '~' is not valid in C var names. Let's replace it.
    # Constructor names in docblock might be just ClassName or ClassName::ClassName.
    # If it's ClassName::ClassName, it becomes PYCNP_doc_ClassName_ClassName
    # If it's ClassName, it becomes PYCNP_doc_ClassName
    # If it's MyFunction, it becomes PYCNP_doc_MyFunction
    
    # Let's make the names compatible with the D() macro.
    # The D macro will take parts like (MyClass, method).
    # docblock gives names like "MyClass::method". We split by "::".
    var_name_parts = [
        part.replace("~", "destructor_").replace("operator=", "operator_assign")
        for part in name.split("::")
    ]
    
    # Construct the full variable name as expected by the D() macro logic
    # e.g., D(MyClass, method) -> PYCNP_doc_MyClass_method
    var_name = "PYCNP_doc_" + "_".join(var_name_parts)

    doc_content = docstrings[0].strip()
    doc_content = doc_content.replace(")doc\"", ")doc\\\"") # Escape R-string terminator

    # Ensure the doc_content is properly placed within the R-string.
    # Literal newlines in doc_content will be preserved by the f-string.
    # We add one newline before and one after the content within the R-string.
    return f'static char const *{var_name} = R"doc(\n{doc_content}\n)doc";'


def parse_args():
    parser = argparse.ArgumentParser(
        description="Extracts C++ docstrings and generates a header file."
    )
    parser.add_argument(
        "output_loc", type=str, help="Path to the output C++ header file."
    )
    parser.add_argument(
        "input_locs", type=str, nargs="+", help="Paths to input C++ header files."
    )
    return parser.parse_args()


def main():
    args = parse_args()

    parsed_docs = {}
    for header_path_str in args.input_locs:
        header_path = Path(header_path_str)
        if not header_path.exists():
            print(f"Warning: Header file not found: {header_path}", file=sys.stderr)
            continue
        try:
            # Using default parsers, usually for C/C++ style comments /** ... */
            # and Doxygen style /// or //!
            # Ensure your C++ comments match what docblock expects.
            # Common format:
            # /**
            #  * Brief description.
            #  *
            #  * More detailed description.
            #  * Parameters:
            #  *   param1 - description
            #  * Returns:
            #  *   description
            #  */
            parsed = docblock.parse_file(header_path)
            # Filter out items with empty docstrings from docblock's result
            for name, docs in parsed.items():
                if docs and docs[0].strip():
                    # Resolve potential duplicates by appending path info or choosing one
                    # For now, later definitions will overwrite earlier ones if names clash across files.
                    # This might be okay if names are fully qualified (e.g. NameSpace::Class::Method)
                    parsed_docs[name] = docs
                    print(f"extract_docstrings.py: Found doc for '{name}'")  # DEBUG PRINT
        except Exception as e:
            print(f"Error parsing {header_path}: {e}", file=sys.stderr)

    if not parsed_docs:
        print("Warning: No docstrings found or extracted.", file=sys.stderr)

    # DEBUG: Print all collected docstring names
    print("extract_docstrings.py: All collected keys in parsed_docs:")
    for key_name in parsed_docs.keys():
        print(f"extract_docstrings.py: - {key_name}")

    # Generate C++ statements for each docstring
    doc_definitions = "\n".join(
        filter(None, (to_cpp_stmt(name, docs) for name, docs in parsed_docs.items()))
    )

    output_path = Path(args.output_loc)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    with open(output_path, "w", encoding="utf-8") as fh:
        fh.write(_PREFIX)
        fh.write(doc_definitions)
        fh.write("\n\n")  # Add some spacing before the suffix
        fh.write(_SUFFIX)

    print(f"Successfully generated docstrings header: {output_path}")


if __name__ == "__main__":
    main() 