"""
性能测试

测试PyCNP的性能特性，验证优化效果。
"""

import pytest
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    import pycnp
    PYCNP_AVAILABLE = True
except ImportError as e:
    PYCNP_AVAILABLE = False
    IMPORT_ERROR = str(e)


@pytest.mark.slow
class TestPerformance:
    """性能测试类"""
    
    def test_model_creation_performance(self):
        """测试Model创建的性能"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        start_time = time.time()
        
        # 创建多个Model实例
        models = []
        for i in range(100):
            model = pycnp.Model()
            models.append(model)
        
        end_time = time.time()
        creation_time = end_time - start_time
        
        # 验证创建时间合理（应该很快）
        assert creation_time < 1.0, f"Model creation took too long: {creation_time:.3f}s"
        assert len(models) == 100
    
    def test_graph_building_performance(self):
        """测试图构建的性能"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        model = pycnp.Model()
        
        start_time = time.time()
        
        # 构建一个中等大小的图
        num_nodes = 100
        for i in range(num_nodes):
            model.add_node(i)
        
        # 添加边（创建一个稀疏图）
        for i in range(num_nodes - 1):
            model.add_edge(i, i + 1)
        
        # 添加一些随机边
        import random
        random.seed(42)
        for _ in range(num_nodes // 2):
            u = random.randint(0, num_nodes - 1)
            v = random.randint(0, num_nodes - 1)
            if u != v:
                model.add_edge(u, v)
        
        end_time = time.time()
        building_time = end_time - start_time
        
        # 验证构建时间合理
        assert building_time < 2.0, f"Graph building took too long: {building_time:.3f}s"
    
    def test_parameter_validation_performance(self):
        """测试参数验证的性能"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        start_time = time.time()
        
        # 创建多个参数实例
        params_list = []
        for i in range(1000):
            params = pycnp.MemeticSearchParams(
                search="CHNS",
                crossover=["DBX", "RSC", "IRR"][i % 3],
                is_pop_variable=i % 2 == 0,
                initial_pop_size=5 + (i % 10)
            )
            params_list.append(params)
        
        end_time = time.time()
        validation_time = end_time - start_time
        
        # 验证参数验证时间合理
        assert validation_time < 1.0, f"Parameter validation took too long: {validation_time:.3f}s"
        assert len(params_list) == 1000


@pytest.mark.integration
class TestIntegration:
    """集成测试类"""
    
    def test_simple_workflow(self):
        """测试简单的工作流程"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        # 创建一个简单的图
        model = pycnp.Model()
        
        # 添加节点
        for i in range(10):
            model.add_node(i)
        
        # 添加边形成一个路径图
        for i in range(9):
            model.add_edge(i, i + 1)
        
        # 验证图结构
        assert len(model.nodes) == 10
        
        # 创建参数
        params = pycnp.MemeticSearchParams(
            initial_population_size=3,
            crossover="RSC"
        )
        
        # 验证参数创建成功
        assert params.initial_population_size == 3
        assert params.crossover == "RSC"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "not slow"])
