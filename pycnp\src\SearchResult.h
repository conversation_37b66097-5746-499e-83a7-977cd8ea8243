#ifndef SEARCH_RESULT_H
#define SEARCH_RESULT_H

#include "Graph/Graph.h"  
#include <climits>       

/**
 * SearchResult
 *
 * 搜索算法结果存储类
 *
 * 用于存储搜索算法的结果，包括找到的解和目标函数值。
 * 支持移动语义以提高性能。
 *
 * Attributes
 * ----------
 * solution
 *     找到的解，表示为节点集合
 * objValue
 *     解的目标函数值
 */
class SearchResult
{
public:
    /**
     * 默认构造函数
     *
     * 将目标函数值初始化为INT_MAX，表示未找到有效解
     */
    SearchResult() : objValue(INT_MAX) {}

    /**
     * 带参数构造函数
     *
     * Parameters
     * ----------
     * sol
     *     解的节点集合
     * obj
     *     目标函数值
     */
    SearchResult(NodeSet sol, int obj) : solution(std::move(sol)), objValue(obj) {}

    // 使用默认的拷贝和移动构造函数
    SearchResult(const SearchResult&) = default;
    SearchResult(SearchResult&&) = default;
    SearchResult& operator=(const SearchResult&) = default;
    SearchResult& operator=(SearchResult&&) = default;

    /// 找到的解，表示为节点集合
    NodeSet solution;

    /// 解的目标函数值
    int objValue;

    /**
     * 检查是否为有效解
     *
     * Returns
     * -------
     * bool
     *     如果目标函数值不是INT_MAX则返回true
     */
    bool isValid() const noexcept { return objValue != INT_MAX; }
};

#endif  // SEARCH_RESULT_H