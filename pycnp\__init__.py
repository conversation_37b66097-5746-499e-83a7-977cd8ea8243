"""
PyCNP - Python bindings for CNP solver package

Provides various algorithms and data structures for solving Critical Node Problem
"""

from typing import Set, Dict, Any, Optional

from ._pycnp import ( # Import from ._pycnp
    # Constants
    CNP, DCNP, NWCNP,      # Problem types
    CBNS, CHNS, DLAS, BCLS, # Search strategies
    DBX, RSC, IRR,         # Crossover strategies

    # Classes
    ProblemData, Graph, CNP_Graph, DCNP_Graph, NWCNP_Graph, SearchResult,
    Population, Search, Crossover,
    VPParams,
)

# Import Python-implemented stopping criteria
from pycnp.stop import (
    StoppingCriterion,
    MaxIterations,
    MaxRuntime,
    NoImprovement,
)

# Import Python-implemented MemeticSearch and parameter classes
from .MemeticSearch import (
    MemeticSearch, 
    MemeticSearchParams,
    VariablePopulationParams,
    
)

# Import Python-implemented Model class
from .Model import Model 

# Import reading functions
from .read import read, get_node_weight

# Import Result class
from .Result import Result

# Import graph visualization functions and classes
from .graph_visualization import visualize_graph

# Version info
__version__ = "0.1.0"

__all__ = [
    # C++ binding constants
    'CNP', 'DCNP', 'NWCNP',  # Problem types
    'CBNS', 'CHNS', 'DLAS', 'BCLS',  # Search strategies
    'DBX', 'RSC', 'IRR',  # Crossover strategies
    
    # C++ binding classes
    'ProblemData',
    'Graph', 'CNP_Graph', 'DCNP_Graph', 'NWCNP_Graph', 'SearchResult', # Added Graph, SearchResult and derived graph classes
    'Population', 'Search', 'Crossover',
    
    # Stopping criteria
    'StoppingCriterion', 'MaxIterations', 'MaxRuntime', 'NoImprovement', 
    
    # Population search
    'MemeticSearch', 
    'MemeticSearchParams',
    'VariablePopulationParams',
    
    
    # Model class
    'Model',
    
    # Result class
    'Result',
    
    # Utility functions
    'read', 'get_node_weight',

    # Graph visualization
    'visualize_graph',
]