import os
import json
import webbrowser
try:
    from pyvis.network import Network
    PYVIS_AVAILABLE = True
except ImportError:
    PYVIS_AVAILABLE = False


def visualize_graph(problem_data, critical_nodes_set, filename="interactive_graph.html"):
    """
    Generate interactive graph visualization HTML file and open it automatically.

    Args:
        problem_data: ProblemData object containing graph nodes and edges information
        critical_nodes_set: Critical nodes set (set or list)
        filename (str): Output HTML filename, defaults to "interactive_graph.html"

    Returns:
        str: Absolute path of the generated HTML file
    """
    if not PYVIS_AVAILABLE:
        raise ImportError("This feature requires 'pyvis' library. Please run: pip install pyvis")

    # Ensure critical_nodes_set is a set type
    if not isinstance(critical_nodes_set, set):
        critical_nodes_set = set(critical_nodes_set)
    
    adj_list = problem_data.get_adj_list()
    # Infer node set from adjacency list
    nodes = set(range(len(adj_list)))

    # --- 1. Pre-calculate component colors for the "after" state ---
    active_nodes = {n for n in nodes if n not in critical_nodes_set}
    visited = set()
    components = []
    for node in active_nodes:
        if node not in visited:
            component = []
            q = [node]
            visited.add(node)
            head = 0
            while head < len(q):
                u = q[head]
                head += 1
                component.append(u)
                for v in adj_list[u]:
                    if v in active_nodes and v not in visited:
                        visited.add(v)
                        q.append(v)
            components.append(component)

    color_palette = [
        "#57C7E5", "#E557C7", "#C7E557", "#E58C57", "#57E58C", 
        "#8C57E5", "#E5B857", "#57A3E5", "#E5578C", "#8CE557"
    ]
    node_to_component_color = {}
    for i, component in enumerate(components):
        color = color_palette[i % len(color_palette)]
        for node in component:
            node_to_component_color[node] = color

    # --- 2. Generate the base pyvis network (initial state) ---
    net = Network(height="95vh", width="100%", bgcolor="#222222", font_color="white", notebook=False)
    default_color = "#97c2fc"
    
    for node in nodes:
        net.add_node(node, label=str(node), color=default_color, title=f"Node {node}")

    for u, neighbors in enumerate(adj_list):
        for v in neighbors:
            if u < v:
                net.add_edge(u, v)
    
    # Save the base network to a string
    html_content = net.generate_html(notebook=False)

    # --- 3. Prepare data and JS code for injection ---
    critical_nodes_list = list(critical_nodes_set)
    
    # Create a comprehensive node info map for JS
    node_info = {}
    for node in nodes:
        info = {
            'component_color': node_to_component_color.get(node, default_color)
        }
        node_info[node] = info

    # Prepare connected components data and sort by size (descending)
    components_data = []
    for i, component in enumerate(components):
        color = color_palette[i % len(color_palette)]
        components_data.append({
            'id': i + 1,
            'nodes': sorted(component),
            'size': len(component),
            'color': color
        })

    # Sort components by size in descending order
    components_data.sort(key=lambda x: x['size'], reverse=True)

    # The custom HTML, CSS, and JavaScript to be injected
    injection_html = f"""
<style>
    #control-panel {{
        position: fixed;
        top: 10px;
        left: 10px;
        padding: 15px;
        background-color: rgba(40, 40, 40, 0.95);
        border: 1px solid #555;
        border-radius: 8px;
        z-index: 1000;
        color: white;
        font-family: sans-serif;
        max-width: 250px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }}
    #control-panel button {{
        display: block;
        width: 100%;
        padding: 10px;
        margin-top: 8px;
        border: 1px solid #666;
        background-color: #333;
        color: white;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 13px;
    }}
    #control-panel button:hover {{
        background-color: #555;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }}

    #search-container {{
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #555;
    }}

    #search-input {{
        width: 100%;
        padding: 8px;
        border: 1px solid #666;
        background-color: #222;
        color: white;
        border-radius: 4px;
        font-size: 13px;
        margin-bottom: 8px;
    }}

    #search-input:focus {{
        outline: none;
        border-color: #4CAF50;
        box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }}

    .search-results {{
        max-height: 100px;
        overflow-y: auto;
        background-color: #222;
        border: 1px solid #555;
        border-radius: 4px;
        margin-top: 5px;
    }}

    .search-result-item {{
        padding: 5px 8px;
        cursor: pointer;
        border-bottom: 1px solid #444;
        font-size: 12px;
    }}

    .search-result-item:hover {{
        background-color: #333;
    }}

    .search-result-item:last-child {{
        border-bottom: none;
    }}

    #components-panel {{
        position: fixed;
        top: 10px;
        right: 10px;
        width: 300px;
        max-height: 80vh;
        padding: 15px;
        background-color: rgba(40, 40, 40, 0.95);
        border: 1px solid #555;
        border-radius: 8px;
        z-index: 1000;
        color: white;
        font-family: sans-serif;
        overflow-y: auto;
        display: none;
    }}

    .component-item {{
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #666;
        border-radius: 6px;
        background-color: rgba(60, 60, 60, 0.8);
    }}

    .component-header {{
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-weight: bold;
    }}

    .component-color {{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
        border: 2px solid #fff;
    }}

    .component-nodes {{
        font-size: 12px;
        line-height: 1.4;
        color: #ccc;
        max-height: 100px;
        overflow-y: auto;
    }}

    .node-tag {{
        display: inline-block;
        background-color: #555;
        color: white;
        padding: 2px 6px;
        margin: 2px;
        border-radius: 3px;
        font-size: 13px;
        cursor: pointer;
    }}

    .node-tag:hover {{
        background-color: #777;
    }}

    .critical-nodes {{
        margin-top: 15px;
        padding: 10px;
        border: 2px solid #ff6961;
        border-radius: 6px;
        background-color: rgba(255, 105, 97, 0.1);
    }}

    .critical-header {{
        color: #ff6961;
        font-weight: bold;
        margin-bottom: 8px;
    }}

    #stats-panel {{
        position: fixed;
        bottom: 80px;
        left: 10px;
        width: 250px;
        padding: 15px;
        background-color: rgba(40, 40, 40, 0.95);
        border: 1px solid #555;
        border-radius: 8px;
        z-index: 1000;
        color: white;
        font-family: sans-serif;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        display: none;
    }}

    .stats-item {{
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
        border-bottom: 1px solid #444;
    }}

    .stats-item:last-child {{
        border-bottom: none;
        margin-bottom: 0;
    }}

    .stats-label {{
        color: #ccc;
        font-size: 13px;
    }}

    .stats-value {{
        color: #4CAF50;
        font-weight: bold;
        font-size: 13px;
    }}
</style>

<div id="control-panel">
    <strong style="font-size: 16px; margin-bottom: 10px; display: block;">🎛️ Control Panel</strong>

    <div id="search-container">
        <input type="text" id="search-input" placeholder="Search nodes (e.g.: 1,2,3 or 1-5)" />
        <div id="search-results" class="search-results" style="display: none;"></div>
    </div>

    <button id="reset-btn">🔄 Reset Graph</button>
    <button id="highlight-btn">🎯 Highlight Critical Nodes</button>
    <button id="isolate-btn">✂️ Remove Critical Nodes</button>
    <button id="toggle-components-btn">📊 Show/Hide Component Info</button>
    <button id="toggle-stats-btn">📈 Show/Hide Statistics</button>
</div>

<div id="components-panel">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <strong style="font-size: 16px;">Connected Components Info</strong>
        <button id="close-components-btn" style="background: none; border: none; color: #ccc; font-size: 18px; cursor: pointer; padding: 0; margin-left: auto;">&times;</button>
    </div>

    <div class="critical-nodes">
        <div class="critical-header">🎯 Critical Nodes (<span id="critical-nodes-count-display"></span>)</div>
        <div id="critical-nodes-list"></div>
    </div>

    <div style="margin-top: 15px; margin-bottom: 10px; font-weight: bold; color: #ccc;">
        🔗 Connected Components
    </div>
    <div id="components-list"></div>
</div>

<div id="stats-panel">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
        <strong style="font-size: 16px;">📈 Graph Statistics</strong>
        <button id="close-stats-btn" style="background: none; border: none; color: #ccc; font-size: 18px; cursor: pointer; padding: 0;">&times;</button>
    </div>

    <div id="stats-content">
        <div class="stats-item">
            <span class="stats-label">Nodes Count:</span>
            <span class="stats-value" id="total-nodes">-</span>
        </div>
        <div class="stats-item">
            <span class="stats-label">Edges Count:</span>
            <span class="stats-value" id="total-edges">-</span>
        </div>
        <div class="stats-item">
            <span class="stats-label">Components Count:</span>
            <span class="stats-value" id="components-count">-</span>
        </div>
        <div class="stats-item">
            <span class="stats-label">Largest Component Size:</span>
            <span class="stats-value" id="largest-component">-</span>
        </div>
        <div class="stats-item">
            <span class="stats-label">Smallest Component Size:</span>
            <span class="stats-value" id="smallest-component">-</span>
        </div>
        <div class="stats-item">
            <span class="stats-label">Average Degree:</span>
            <span class="stats-value" id="average-degree">-</span>
        </div>
    </div>
</div>

<script type="text/javascript">
    // Wait for the network to be fully rendered and stabilized
    network.on("stabilizationIterationsDone", function () {{
        // Data from Python
        const CRITICAL_NODES = {json.dumps(critical_nodes_list)};
        const NODE_INFO = {json.dumps(node_info)};
        const COMPONENTS_DATA = {json.dumps(components_data)};
        const CRITICAL_COLOR = "#ff6961";
        const DEFAULT_COLOR = "{default_color}";

        // Store the original full edge set and node set
        const allEdges = new vis.DataSet(network.body.data.edges.get());
        const allNodes = new vis.DataSet(network.body.data.nodes.get());

        const nodesDataSet = network.body.data.nodes;
        const edgesDataSet = network.body.data.edges;

        // Calculate graph statistics
        function calculateStats() {{
            const allNodes = nodesDataSet.get();
            const allEdges = edgesDataSet.get();

            const totalNodes = allNodes.length;
            const totalEdges = allEdges.length;
            const componentsCount = COMPONENTS_DATA.length;
            const largestComponent = COMPONENTS_DATA.length > 0 ? Math.max(...COMPONENTS_DATA.map(c => c.size)) : 0;
            const smallestComponent = COMPONENTS_DATA.length > 0 ? Math.min(...COMPONENTS_DATA.map(c => c.size)) : 0;
            const avgDegree = totalNodes > 0 ? (2 * totalEdges / totalNodes).toFixed(2) : 0;

            return {{
                totalNodes,
                totalEdges,
                componentsCount,
                largestComponent,
                smallestComponent,
                avgDegree
            }};
        }}

        // Update statistics panel
        function updateStatsPanel() {{
            const stats = calculateStats();
            document.getElementById('total-nodes').textContent = stats.totalNodes;
            document.getElementById('total-edges').textContent = stats.totalEdges;
            document.getElementById('components-count').textContent = stats.componentsCount;
            document.getElementById('largest-component').textContent = stats.largestComponent;
            document.getElementById('smallest-component').textContent = stats.smallestComponent;
            document.getElementById('average-degree').textContent = stats.avgDegree;
        }}

        // Initialize connected components panel
        function initializeComponentsPanel() {{
            // Display critical nodes count
            document.getElementById('critical-nodes-count-display').textContent = CRITICAL_NODES.length;

            // Display critical nodes (sorted by node number)
            const criticalNodesList = document.getElementById('critical-nodes-list');
            if (CRITICAL_NODES.length > 0) {{
                const sortedCriticalNodes = [...CRITICAL_NODES].sort((a, b) => a - b);
                criticalNodesList.innerHTML = sortedCriticalNodes.map(node =>
                    `<span class="node-tag" onclick="focusOnNode(${{node}})">${{node}}</span>`
                ).join('');
            }} else {{
                criticalNodesList.innerHTML = '<span style="color: #999;">No critical nodes</span>';
            }}

            // Display connected components (already sorted by size)
            const componentsList = document.getElementById('components-list');
            if (COMPONENTS_DATA.length > 0) {{
                componentsList.innerHTML = COMPONENTS_DATA.map((comp, index) => `
                    <div class="component-item">
                        <div class="component-header">
                            <div class="component-color" style="background-color: ${{comp.color}};"></div>
                            <span>Component ${{index + 1}} (${{comp.size}})</span>
                        </div>
                        <div class="component-nodes">
                            ${{comp.nodes.map(node =>
                                `<span class="node-tag" onclick="focusOnNode(${{node}})">${{node}}</span>`
                            ).join('')}}
                        </div>
                    </div>
                `).join('');
            }} else {{
                componentsList.innerHTML = '<div style="color: #999; text-align: center; padding: 20px;">Connected components will be shown after removing critical nodes</div>';
            }}
        }}

        // Node search functionality
        function initializeSearch() {{
            const searchInput = document.getElementById('search-input');
            const searchResults = document.getElementById('search-results');

            searchInput.addEventListener('input', function() {{
                const query = this.value.trim();
                if (query === '') {{
                    searchResults.style.display = 'none';
                    return;
                }}

                const results = searchNodes(query);
                displaySearchResults(results);
            }});

            searchInput.addEventListener('keypress', function(e) {{
                if (e.key === 'Enter') {{
                    const query = this.value.trim();
                    const results = searchNodes(query);
                    if (results.length > 0) {{
                        focusOnNode(results[0]);
                        searchResults.style.display = 'none';
                    }}
                }}
            }});
        }}

        function searchNodes(query) {{
            const allNodes = nodesDataSet.get().map(n => n.id);
            const results = [];

            // Support single node search
            if (/^\\d+$/.test(query)) {{
                const nodeId = parseInt(query);
                if (allNodes.includes(nodeId)) {{
                    results.push(nodeId);
                }}
            }}
            // Support range search (e.g.: 1-5)
            else if (/^\\d+-\\d+$/.test(query)) {{
                const [start, end] = query.split('-').map(n => parseInt(n));
                for (let i = Math.min(start, end); i <= Math.max(start, end); i++) {{
                    if (allNodes.includes(i)) {{
                        results.push(i);
                    }}
                }}
            }}
            // Support comma-separated multiple nodes (e.g.: 1,3,5)
            else if (/^[\\d,\\s]+$/.test(query)) {{
                const nodeIds = query.split(',').map(n => parseInt(n.trim())).filter(n => !isNaN(n));
                nodeIds.forEach(nodeId => {{
                    if (allNodes.includes(nodeId)) {{
                        results.push(nodeId);
                    }}
                }});
            }}

            return results;
        }}

        function displaySearchResults(results) {{
            const searchResults = document.getElementById('search-results');

            if (results.length === 0) {{
                searchResults.innerHTML = '<div class="search-result-item">No matching nodes found</div>';
            }} else {{
                searchResults.innerHTML = results.slice(0, 10).map(nodeId =>
                    `<div class="search-result-item" onclick="focusOnNode(${{nodeId}}); document.getElementById('search-results').style.display='none';">
                        Node ${{nodeId}} ${{CRITICAL_NODES.includes(nodeId) ? '(Critical Node)' : ''}}
                    </div>`
                ).join('');

                if (results.length > 10) {{
                    searchResults.innerHTML += '<div class="search-result-item" style="color: #999;">Showing first 10 results...</div>';
                }}
            }}

            searchResults.style.display = 'block';
        }}

        // Focus on specific node
        function focusOnNode(nodeId) {{
            network.selectNodes([nodeId]);
            network.focus(nodeId, {{
                scale: 1.5,
                animation: {{
                    duration: 1000,
                    easingFunction: "easeInOutQuad"
                }}
            }});

            // Highlight node
            const updates = [];
            nodesDataSet.getIds().forEach(id => {{
                let color = id === nodeId ? '#FFD700' : (CRITICAL_NODES.includes(id) ? CRITICAL_COLOR : DEFAULT_COLOR);
                updates.push({{id: id, color: color}});
            }});
            nodesDataSet.update(updates);

            // Restore original color after 3 seconds
            setTimeout(() => {{
                const updates = [];
                nodesDataSet.getIds().forEach(id => {{
                    let color = CRITICAL_NODES.includes(id) ? CRITICAL_COLOR : DEFAULT_COLOR;
                    updates.push({{id: id, color: color}});
                }});
                nodesDataSet.update(updates);
            }}, 3000);
        }}

        function resetGraph() {{
            // Restore all original nodes
            nodesDataSet.clear();
            nodesDataSet.add(allNodes.get());

            // Restore all original edges
            edgesDataSet.clear();
            edgesDataSet.add(allEdges.get());

            // Reset all node colors to default color
            const updates = [];
            nodesDataSet.getIds().forEach(id => {{
                updates.push({{id: id, color: DEFAULT_COLOR}});
            }});
            nodesDataSet.update(updates);

            network.fit();
        }}

        function highlightNodes() {{
            const updates = [];
            nodesDataSet.getIds().forEach(id => {{
                let color = CRITICAL_NODES.includes(id) ? CRITICAL_COLOR : DEFAULT_COLOR;
                updates.push({{id: id, color: color}});
            }});
            nodesDataSet.update(updates);
        }}

        function isolateNodes() {{
            // Completely remove critical nodes
            const nodesToRemove = [];
            const edgesToRemove = [];

            // Collect critical nodes to remove
            nodesDataSet.get().forEach(node => {{
                if (CRITICAL_NODES.includes(node.id)) {{
                    nodesToRemove.push(node.id);
                }}
            }});

            // Collect edges connected to critical nodes
            edgesDataSet.get().forEach(edge => {{
                if (CRITICAL_NODES.includes(edge.from) || CRITICAL_NODES.includes(edge.to)) {{
                    edgesToRemove.push(edge.id);
                }}
            }});

            // Remove edges and nodes
            edgesDataSet.remove(edgesToRemove);
            nodesDataSet.remove(nodesToRemove);

            // Update remaining nodes' colors to component colors
            const nodeUpdates = [];
            nodesDataSet.getIds().forEach(id => {{
                if (!CRITICAL_NODES.includes(id)) {{
                    let color = NODE_INFO[id].component_color;
                    nodeUpdates.push({{id: id, color: color}});
                }}
            }});
            nodesDataSet.update(nodeUpdates);
        }}

        // Toggle connected components panel display
        function toggleComponentsPanel() {{
            const panel = document.getElementById('components-panel');
            if (panel.style.display === 'none') {{
                panel.style.display = 'block';
            }} else {{
                panel.style.display = 'none';
            }}
        }}

        // Toggle statistics panel display
        function toggleStatsPanel() {{
            const panel = document.getElementById('stats-panel');
            if (panel.style.display === 'none') {{
                panel.style.display = 'block';
                updateStatsPanel();
            }} else {{
                panel.style.display = 'none';
            }}
        }}

        // Close connected components panel
        function closeComponentsPanel() {{
            document.getElementById('components-panel').style.display = 'none';
        }}

        // Close statistics panel
        function closeStatsPanel() {{
            document.getElementById('stats-panel').style.display = 'none';
        }}

        // Initialize all features
        initializeComponentsPanel();
        initializeSearch();
        updateStatsPanel();

        // Attach event listeners
        document.getElementById('reset-btn').onclick = resetGraph;
        document.getElementById('highlight-btn').onclick = highlightNodes;
        document.getElementById('isolate-btn').onclick = isolateNodes;
        document.getElementById('toggle-components-btn').onclick = toggleComponentsPanel;
        document.getElementById('toggle-stats-btn').onclick = toggleStatsPanel;
        document.getElementById('close-components-btn').onclick = closeComponentsPanel;
        document.getElementById('close-stats-btn').onclick = closeStatsPanel;

        // Hide search results when clicking elsewhere
        document.addEventListener('click', function(e) {{
            if (!e.target.closest('#search-container')) {{
                document.getElementById('search-results').style.display = 'none';
            }}
        }});

        console.log("Enhanced interactive controls initialized.");
        console.log("Components data:", COMPONENTS_DATA);
        console.log("Available features: Search, Statistics, Component Analysis");
    }});
</script>
"""

    # Write HTML content to file
    final_html = html_content.replace("</body>", injection_html + "</body>")

    with open(filename, "w", encoding="utf-8") as f:
        f.write(final_html)

    abs_path = os.path.abspath(filename)
    print(f"Interactive graph visualization saved to: {abs_path}")

    # Automatically open browser
    try:
        webbrowser.open(f'file://{abs_path}')
        print("Visualization page opened in default browser")
    except Exception as e:
        print(f"Unable to automatically open browser: {e}")
        print(f"Please manually open file: {abs_path}")

    return abs_path

