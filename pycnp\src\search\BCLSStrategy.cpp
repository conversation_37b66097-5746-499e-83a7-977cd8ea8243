#include "BCLSStrategy.h"
#include "Graph/DCNP_Graph.h"
#include "SearchResult.h"
#include <algorithm>
#include <cstdlib>
#include <ctime>
#include <iostream>
#include <iterator>
#include <list>
#include <stdexcept>
#include <vector>

BCLSStrategy::BCLSStrategy(
    Graph &graph, const std::unordered_map<std::string, std::any> &params)
    : graph(graph), params(params)
{

    auto it = params.find("maxIdleSteps");
    if (it != params.end())
    {
        try
        {
            maxIdleSteps = std::any_cast<int>(it->second);
        }
        catch (const std::bad_any_cast &)
        {
        }
    }

    it = params.find("selectionProb");
    if (it != params.end())
    {
        try
        {
            selectionProb = std::any_cast<double>(it->second);
        }
        catch (const std::bad_any_cast &)
        {
        }
    }

    it = params.find("seed");
    if (it != params.end())
    {
        try
        {
            int seed = std::any_cast<int>(it->second);

            if (seed > 0)
            {
                rng.setSeed(seed);

                std::srand(static_cast<unsigned int>(seed));
            }
        }
        catch (const std::bad_any_cast &)
        {

            std::srand(static_cast<unsigned int>(time(nullptr)));
        }
    }
    else
    {

        std::srand(static_cast<unsigned int>(time(nullptr)));
    }
}

SearchResult BCLSStrategy::execute()
{

    SearchResult result;

    Graph &currentGraph = graph;
    NodeSet bestSolution;

    DCNP_Graph *dcnpGraph = dynamic_cast<DCNP_Graph *>(&currentGraph);

    bestSolution = dcnpGraph->getRemovedNodes();

    int currentObjValue = graph.getObjectiveValue();
    int bestObjValue = currentObjValue;
    long numIdleSteps = 0;

    std::vector<Node> sortedNodes;
    sortedNodes.reserve(currentGraph.getNumNodes());
    for (Node i = 0; i < currentGraph.getNumNodes(); ++i)
    {
        sortedNodes.push_back(i);
    }

    const std::vector<double> &centrality
        = currentGraph.calculateBetweennessCentrality();
    std::sort(sortedNodes.begin(),
              sortedNodes.end(),
              [&centrality](int a, int b)
              { return centrality[a] > centrality[b]; });

    std::list<Node> candidateNodes(sortedNodes.begin(), sortedNodes.end());

    auto it_5 = candidateNodes.begin();
    int steps = std::min(5, static_cast<int>(candidateNodes.size()));
    if (steps > 0)
    {
        std::advance(it_5, steps - 1);
    }

    while (numIdleSteps < maxIdleSteps)
    {

        performMove(currentGraph, currentObjValue, candidateNodes, it_5);

        if (currentObjValue < bestObjValue)
        {
            bestSolution = currentGraph.getRemovedNodes(); // 现在返回const引用，会自动拷贝
            bestObjValue = currentObjValue;
            numIdleSteps = 0;
        }
        else
        {
            numIdleSteps++;
        }
    }

    result.solution = bestSolution;
    result.objValue = bestObjValue;

    return result;
}

void BCLSStrategy::performMove(Graph &currentGraph,
                               int &currentObjValue,
                               std::list<Node> &candidateNodes,
                               std::list<Node>::iterator &it_5)
{

    if (candidateNodes.empty())
    {
        std::cout << "candidate nodes list is empty, can not perform move" << std::endl;
        return;
    }

    while (!candidateNodes.empty())
    {

        double r = rng.generateProbability();

        Node removedNode = candidateNodes.front();
        candidateNodes.pop_front();

        if (candidateNodes.size() >= 5)
        {
            it_5 = candidateNodes.begin();
            std::advance(it_5, 4);
        }
        else if (!candidateNodes.empty())
        {
            it_5 = std::prev(candidateNodes.end());
        }
        else
        {
            it_5 = candidateNodes.end();
        }

        if (currentGraph.isNodeRemoved(removedNode))
        {
            continue;
        }

        if (r < selectionProb)
        {

            currentGraph.removeNode(removedNode);

            Node BestNodeToAdd = currentGraph.findBestNodeToAdd();

            if (BestNodeToAdd != Graph::INVALID_NODE)
            {
                currentGraph.addNode(BestNodeToAdd);

                candidateNodes.push_back(BestNodeToAdd);

                if (candidateNodes.size() >= 5)
                {
                    it_5 = candidateNodes.begin();
                    std::advance(it_5, 4);
                }
                else if (!candidateNodes.empty())
                {
                    it_5 = std::prev(candidateNodes.end());
                }
                else
                {
                    it_5 = candidateNodes.end();
                }
            }
            else
            {

                candidateNodes.push_front(removedNode);

                if (candidateNodes.size() >= 5)
                {
                    it_5 = candidateNodes.begin();
                    std::advance(it_5, 4);
                }
                else if (!candidateNodes.empty())
                {
                    it_5 = std::prev(candidateNodes.end());
                }
                else
                {
                    it_5 = candidateNodes.end();
                }
                continue;
            }

            currentObjValue = currentGraph.getObjectiveValue();

            return;
        }
        else
        {

            if (candidateNodes.size() >= 5 && it_5 != candidateNodes.end())
            {

                candidateNodes.insert(std::next(it_5), removedNode);
            }
            else
            {

                candidateNodes.push_back(removedNode);
            }

            if (candidateNodes.size() >= 5)
            {
                it_5 = candidateNodes.begin();
                std::advance(it_5, 4);
            }
            else if (!candidateNodes.empty())
            {
                it_5 = std::prev(candidateNodes.end());
            }
            else
            {
                it_5 = candidateNodes.end();
            }
        }
    }
}