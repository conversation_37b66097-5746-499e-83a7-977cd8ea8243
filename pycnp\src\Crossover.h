#ifndef CROSSOVER_H
#define CROSSOVER_H

#include "Graph/CNP_Graph.h"
#include "Graph/DCNP_Graph.h"
#include "Graph/NWCNP_Graph.h"
#include "Population.h"
#include "RandomNumberGenerator.h"
#include <memory>
#include <tuple>
#include <vector>

/**
 * Crossover
 *
 * 交叉算子类
 *
 * 提供三种交叉方法：doubleBackboneBasedCrossover、reduce-solve-combine 和 inherit_repair_recombination
 * 用于从父代解生成子代解
 */
class Crossover
{
private:
    // 双骨干交叉算法参数
    static constexpr double THETA = 0.85;       ///< 双骨干交叉中选择独占节点的概率阈值
    static constexpr double BETA = 0.9;         ///< IR中选择公共节点的概率

    // 规约-求解-合并交叉算法参数
    static constexpr double TARGET_RATIO = 0.9; ///< 基于频率选择节点的目标比例
    static constexpr double P2 = 0.5;           ///< 选择频率为2的节点的概率
    static constexpr double P1 = 0.9;           ///< 不选择频率2时选择频率1节点的概率

    RandomNumberGenerator rng;                   ///< 随机数生成器
    int seed;                                    ///< 随机种子

public:
    /**
     * Constructor for Crossover class
     *
     * Parameters
     * ----------
     * seed
     *     Seed value for initializing the random number generator
     */
    Crossover(int seed);

    /**
     * Double backbone crossover algorithm - mainly used for CNP and NWCNP problems
     * Generates new offspring solutions based on common backbone and exclusive nodes of two parent solutions
     *
     * Parameters
     * ----------
     * originalGraph
     *     The original graph
     * parents
     *     Pair of parent solutions
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     New graph (offspring solution) generated through crossover operation
     */
    std::unique_ptr<Graph> doubleBackboneBasedCrossover(
        const Graph &originalGraph,
        const std::pair<const Solution *, const Solution *> &parents);

    /**
     * Reduce-Solve-Combine crossover method
     *
     * Parameters
     * ----------
     * originalGraph
     *     The original graph instance
     * parents
     *     Pair of parent solutions
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     New graph (offspring solution) generated through crossover operation
     */
    std::unique_ptr<Graph> reduceSolveCombine(
        const Graph &originalGraph,
        const std::pair<const Solution *, const Solution *> &parents);

    /**
     * Inherit repair recombination crossover method
     *
     * Parameters
     * ----------
     * originalGraph
     *     The original graph instance
     * parents
     *     Tuple of three parent solutions
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     New graph (offspring solution) generated through crossover operation
     */
    std::unique_ptr<Graph> inherit_repair_recombination(
        const Graph &originalGraph,
        const std::tuple<const Solution *, const Solution *, const Solution *>
            &parents);
};

#endif  // CROSSOVER_H