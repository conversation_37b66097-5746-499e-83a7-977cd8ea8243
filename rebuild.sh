#!/bin/bash
# PyCNP project rebuild script - using Meson-Python build system

set -e  # Exit immediately on error

echo "🧹 Cleaning old build files..."
rm -rf build/
rm -rf pycnp.egg-info/
rm -rf dist/
rm -f pycnp/_pycnp.so  # Remove possible old symlinks

echo "📦 Installing build dependencies..."
# Install build dependencies if not already installed
pip install -q meson-python pybind11 meson ninja

echo "📦 Installing project dependencies..."
# Install runtime dependencies
pip install -q numpy>=1.19.0

echo "🏗️  Configuring Meson build..."
meson setup build --reconfigure

echo "⚡ Compiling C++ extensions..."
meson compile -C build

echo "🔗 Creating symlink to project root directory..."
BUILD_PATH=$(find $(pwd)/build -name "_pycnp*.so" | head -1)
if [ -n "$BUILD_PATH" ]; then
    echo "✅ Found compiled module: $BUILD_PATH"
    ln -sf "$BUILD_PATH" $(pwd)/pycnp/_pycnp.so
    echo "🔗 Symlink created successfully: $(pwd)/pycnp/_pycnp.so -> $BUILD_PATH"
else
    echo "❌ Error: Compiled module file not found"
    echo "   Please check if there were any compilation errors"
    exit 1
fi

echo "🧪 Testing module import..."
if python -c "import sys; sys.path.insert(0, '.'); import pycnp; print('✅ PyCNP module imported successfully!')"; then
    echo "🎉 Compilation complete! PyCNP module is ready"
else
    echo "❌ Module import test failed"
    exit 1
fi

echo ""
echo "📚 使用说明:"
echo "   • 在项目目录下运行Python: python"
echo "   • 运行示例: python examples/model-CNP.py"
echo "   • 或者直接使用: python examples/model-DCNP.py"
echo ""
echo "🔧 开发工具:"
echo "   • 安装开发依赖: pip install -e .[dev]"
echo "   • 代码格式化: black pycnp/"
echo "   • 导入排序: isort pycnp/"
echo "   • 重新构建: ./rebuild.sh"
echo ""
echo "💡 提示: 如果您想在任何地方都能导入PyCNP模块，请将以下行添加到您的~/.bashrc或~/.zshrc文件中:"
echo "   export PYTHONPATH=$(pwd):\$PYTHONPATH"