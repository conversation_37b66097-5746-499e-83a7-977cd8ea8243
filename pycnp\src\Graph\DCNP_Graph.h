#ifndef DCNP_GRAPH_H
#define DCNP_GRAPH_H

#include "../RandomNumberGenerator.h"
#include "Graph.h"
#include <algorithm>
#include <list>
#include <memory>
#include <numeric>
#include <queue>
#include <stack>
#include <stdexcept>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

/**
 * DCNP_Graph
 *
 * Concrete implementation of the Distance-based Node Problem (DCNP) graph model.
 *
 * Inheriting from the abstract base class `Graph`, `DCNP_Graph` provides specialized
 * graph structures and algorithms for the DCNP problem. Core parameters include K-hop
 * limit (`kHops`) and the number of nodes allowed to be removed (`numToRemove`).
 * The objective function typically minimizes the total size of K-hop reachable
 * neighborhoods for all nodes (`calculateKHopTreeSize`). This class implements node
 * addition/removal, K-hop tree construction (`bfs_k_tree`, `build_tree`), and methods
 * for selecting optimal nodes to remove/add based on heuristics (such as betweenness
 * centrality `calculateBetweennessCentrality` or impact on the objective function).
 */
class DCNP_Graph : public Graph
{
private:
    int numNodes;     ///< Total number of nodes in the graph (usually the original graph).
    int kHops;        ///< K-hop limit. Defines the maximum distance (hops) considered in reachability calculations.
    int numToRemove;  ///< Budget parameter B, representing the upper limit on the number of nodes allowed to be removed.
    NodeSet originalNodesSet;  ///< Set storing all original nodes in the graph, for reference.
    std::vector<Age> nodeAge;  ///< Stores the "age" of each node, used in certain heuristic algorithms.

    std::vector<NodeSet> currentAdjList;  ///< Adjacency list representing the current state of the graph.
    std::vector<NodeSet> originalAdjList;  ///< Adjacency list representing the original structure of the graph, typically unchanged during modifications.

    /**
     * K-hop tree adjacency information.
     *
     * `intree[v]` is a vector where `intree[v][u] = 1` indicates that node `u` is within
     * node `v`'s K-hop tree (i.e., `u` is reachable from `v` within K hops), otherwise 0.
     * This is used for quick queries about whether two nodes are K-hop reachable in the current graph state.
     */
    std::vector<std::vector<int>> intree;

    /**
     * K-hop tree sizes for each node.
     *
     * `treeSize[v]` stores the total number of nodes reachable from node `v` within its
     * K-hop neighborhood (including `v` itself). The sum of all nodes' `treeSize` values
     * is typically the DCNP objective function.
     */
    std::vector<int> treeSize;
    NodeSet removedNodes;  ///< Set storing nodes currently removed (or selected for removal).

    RandomNumberGenerator rng;  ///< Random number generator instance for random decision processes within the class.

    /**
     * (Internal helper) Builds the K-hop tree for a specified starting node `v` using breadth-first search (BFS).
     *
     * This method performs BFS starting from node `v`, exploring all reachable nodes within
     * its K-hop range. Results update `intree[v]` and `treeSize[v]` to reflect node `v`'s
     * K-hop neighborhood. Search is only conducted between currently unremoved nodes
     * (`!isNodeRemoved(neighbor)`).
     *
     * Parameters
     * ----------
     * v
     *     ID of the starting node for which to build the K-hop tree.
     */
    void bfs_k_tree(Node v);

public:
    /**
     * DCNP_Graph constructor.
     *
     * Initializes a DCNP graph instance.
     *
     * Parameters
     * ----------
     * nodes
     *     `NodeSet` containing all original node IDs in the graph.
     * K
     *     Integer, K-hop limit defining the reachability distance.
     * adjList
     *     Adjacency list representation of the graph, where `adjList[i]` is a `NodeSet`
     *     containing all neighbors of node `i`.
     * numToRemove
     *     Integer, upper limit on the number of nodes allowed to be removed from the graph.
     * seed
     *     Integer seed for initializing the internal random number generator.
     */
    DCNP_Graph(NodeSet nodes,
               int K,
               std::vector<NodeSet> adjList,
               int numToRemove,
               int seed);

    /**
     * DCNP_Graph default constructor.
     *
     * Creates an empty or default-state `DCNP_Graph` object.
     * May require subsequent initialization calls to make it usable.
     */
    DCNP_Graph() = default;

    /**
     * Update the current graph's state based on a set of nodes to be removed.
     *
     * Implements the base class's pure virtual function. This method modifies the graph
     * to reflect the removal of nodes in the `nodesToRemove` set. Key operations include
     * updating the `removedNodes` set and calling `build_tree()` to recalculate K-hop
     * tree information (`intree` and `treeSize`) for all nodes, as node removal affects
     * global K-hop reachability.
     *
     * Parameters
     * ----------
     * nodesToRemove
     *     Set of nodes to be removed from the graph.
     */
    void updateGraphByRemovedNodes(const NodeSet &nodesToRemove) override;

    /**
     * Get a "reduced" graph based on a set of nodes to be removed.
     *
     * Implements the base class's pure virtual function. For `DCNP_Graph`, this operation
     * is typically equivalent to `updateGraphByRemovedNodes`, i.e., modifying the current
     * graph instance to reflect node removal and rebuilding K-hop information.
     *
     * Parameters
     * ----------
     * nodesToRemove
     *     Set of nodes to be removed from the graph.
     */
    void getReducedGraphByRemovedNodes(const NodeSet &nodesToRemove) override;

    /**
     * Permanently remove a single specified node from the graph.
     *
     * Implements the base class's pure virtual function. Adds node `nodeToRemove` to the
     * `removedNodes` set. Afterwards, `build_tree()` must be called to update all K-hop
     * tree information, as removing a single node may widely affect K-hop reachability
     * between other nodes in the graph.
     *
     * Parameters
     * ----------
     * nodeToRemove
     *     ID of the node to be removed from the graph.
     */
    void removeNode(Node nodeToRemove) override;

    /**
     * Add (or restore) a single specified node to the graph.
     *
     * Implements the base class's pure virtual function. If node `nodeToAdd` exists in
     * `removedNodes`, it is removed from the `removedNodes` set. Then, `build_tree()`
     * must be called to update all K-hop tree information to reflect possible changes
     * in K-hop reachability after node restoration.
     *
     * Parameters
     * ----------
     * nodeToAdd
     *     ID of the node to be added to the graph.
     */
    void addNode(Node nodeToAdd) override;

    /**
     * Set the "age" attribute for a specific node in the graph.
     *
     * Implements the base class's pure virtual function. Updates the value in the
     * `nodeAge` vector for the corresponding node.
     *
     * Parameters
     * ----------
     * node
     *     ID of the node whose age is to be set.
     * age
     *     Age value to be set for this node (`Age` type).
     */
    void setNodeAge(Node node, Age age) override { nodeAge[node] = age; }

    /**
     * Check if a specified node is currently in a "removed" state.
     *
     * Implements the base class's pure virtual function. Determined by checking the
     * `removedNodes` set.
     *
     * Parameters
     * ----------
     * node
     *     ID of the node whose removal status is to be checked.
     *
     * Returns
     * -------
     * bool
     *     Returns `true` if the node is in `removedNodes`; otherwise returns `false`.
     */
    bool isNodeRemoved(Node node) const override
    {
        return removedNodes.count(node) > 0;
    }

    /**
     * Get the set of all nodes currently marked as "removed" in the graph.
     *
     * Implements the base class's pure virtual function. Returns a copy or constant
     * reference to the `removedNodes` member.
     *
     * Returns
     * -------
     * const NodeSet&
     *     A constant reference to the `NodeSet` containing IDs of all currently removed nodes.
     */
    const NodeSet& getRemovedNodes() const override { return removedNodes; }

    /**
     * Get the total number of nodes in the graph (usually referring to the original graph).
     *
     * Implements the base class's pure virtual function. Returns the value of the
     * `numNodes` member.
     *
     * Returns
     * -------
     * int
     *     Total number of nodes in the graph.
     */
    int getNumNodes() const override { return numNodes; }

    /**
     * Get the objective function value for the current graph state, i.e., the sum of
     * all nodes' K-hop tree sizes.
     *
     * Implements the base class's pure virtual function. Gets the current objective
     * value by calling `calculateKHopTreeSize()`.
     *
     * Returns
     * -------
     * int
     *     Sum of all nodes' K-hop tree sizes in the current graph.
     */
    int getObjectiveValue() const override { return calculateKHopTreeSize(); }

    /**
     * (DCNP) Generate and return a random feasible instance (solution) of the current graph.
     *
     * Implements the base class's pure virtual function. Typically creates a copy of
     * the current graph, then randomly selects `numToRemove` different nodes for removal,
     * forming a random, budget-constrained solution. Afterwards updates K-hop tree
     * information for this copy.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     Smart pointer to a newly generated `DCNP_Graph` instance representing a random
     *     feasible solution.
     */
    std::unique_ptr<Graph> getRandomFeasibleGraph() override;

    /**
     * (DCNP) Build or rebuild K-hop tree information for all (unremoved) nodes in the current graph.
     *
     * Overrides the base class's virtual function. This method traverses all currently
     * existing nodes in the graph and calls `bfs_k_tree()` for each node to calculate
     * its K-hop neighborhood, updating the `intree` and `treeSize` members.
     * This is a crucial step in maintaining correct objective function calculations
     * after graph structure (node removal/addition) changes.
     */
    void build_tree() override;

    /**
     * (DCNP) Calculate and return the sum of all nodes' K-hop tree sizes in the current graph.
     *
     * Overrides the base class's virtual function. This method traverses the `treeSize`
     * vector (which should be updated by `build_tree()`), accumulating the K-hop tree
     * sizes of all nodes to get the DCNP objective function value.
     *
     * Returns
     * -------
     * int
     *     Sum of K-hop tree sizes for all (unremoved) nodes.
     */
    int calculateKHopTreeSize() const override;

    /**
     * (DCNP) Calculate betweenness centrality for each active node in the graph.
     *
     * Overrides the base class's virtual function. This method calculates betweenness
     * centrality for all unremoved nodes in the current graph. Betweenness centrality
     * measures how frequently a node appears on shortest paths between other node pairs,
     * often used as a heuristic indicator for identifying critical nodes.
     *
     * Returns
     * -------
     * const std::vector<double>&
     *     Constant reference to a vector containing betweenness centrality values for
     *     each active node. Vector indices correspond to node IDs, values are the nodes'
     *     betweenness centrality. Values may be undefined or 0 for removed nodes or
     *     non-existent indices.
     */
    const std::vector<double> &calculateBetweennessCentrality() const override;

    /**
     * (DCNP) Find the "best" node to remove from the current graph based on heuristics
     * (typically the one with maximum impact on the objective function).
     *
     * Overrides the base class's virtual function. This method evaluates the expected
     * impact on the objective function (total K-hop tree size) of removing each
     * candidate node not yet removed from the current graph. Selects the node that
     * would most "damage" K-hop reachability. Prerequisite is that the number of
     * removed nodes is less than `numToRemove`.
     *
     * Returns
     * -------
     * Node
     *     ID of the node selected as the best removal candidate. Returns `INVALID_NODE`
     *     if removal limit is reached or no nodes are available.
     */
    Node findBestNodeToRemove() override;

    /**
     * (DCNP) Find the "best" node to add back to the graph from removed nodes based on
     * heuristics (typically the one with maximum improvement to the objective function).
     *
     * Overrides the base class's virtual function. This method evaluates the expected
     * improvement to the objective function (total K-hop tree size) of adding back each
     * currently removed node. Selects the node that would best restore K-hop reachability.
     *
     * Returns
     * -------
     * Node
     *     ID of the node selected as the best addition candidate. Returns `INVALID_NODE`
     *     if no removed nodes exist or no improvement is possible.
     */
    Node findBestNodeToAdd() override;

    /**
     * (DCNP) Randomly select a node from the graph (usually from removable candidates)
     * for removal.
     *
     * Overrides the base class's virtual function. Randomly selects a node from those
     * currently not removed. Prerequisite is that the number of removed nodes is less
     * than `numToRemove`.
     *
     * Returns
     * -------
     * Node
     *     ID of the randomly selected node for removal. Returns `INVALID_NODE` if
     *     removal limit is reached or no nodes are available.
     */
    Node randomSelectNodeToRemove() const override;

    /**
     * Create a deep copy of the current `DCNP_Graph` object.
     *
     * Implements the base class's pure virtual function. This method creates a new
     * `DCNP_Graph` object that is a complete, independent copy of the current instance,
     * including the state of all member variables (such as adjacency lists, K-hop
     * information, removed nodes, etc.).
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     `std::unique_ptr` pointing to the newly created `DCNP_Graph` object
     *     (return type is base class `Graph` pointer to support polymorphism).
     */
    std::unique_ptr<Graph> clone() const override;
};

#endif  // DCNP_GRAPH_H
