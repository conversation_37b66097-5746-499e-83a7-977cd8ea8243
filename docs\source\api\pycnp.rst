.. module:: pycnp
   :synopsis: PyCNP

PyCNP
=====

The top-level :mod:`pyvrp` module exposes several core classes needed to run the VRP solver.
These include the core :class:`~pyvrp.GeneticAlgorithm.GeneticAlgorithm`, and the :class:`~pyvrp.Population.Population` that manages a :class:`~pyvrp._pyvrp.Solution` pool.
Most classes take parameter objects that allow for advanced configuration - but sensible defaults are also provided.
Finally, after running, the :class:`~pyvrp.GeneticAlgorithm.GeneticAlgorithm` returns a :class:`~pyvrp.Result.Result` object.
This object can be used to obtain the best observed solution, and detailed runtime statistics.

.. automodule:: pycnp.Model
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: pycnp.MemeticSearch
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: pycnp.read
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: pycnp.Result
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: pycnp.Statistics
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: pycnp.ProgressPrinter
   :members:
   :undoc-members:
   :show-inheritance:

   .. autoclass:: ProblemData
      :members:
      :undoc-members:
      :show-inheritance:

      Problem data manager responsible for graph construction, storage, and efficient access.
      Provides unified interface for different problem types and graph formats.

   .. autoclass:: Population
      :members:
      :undoc-members:
      :show-inheritance:


   .. autoclass:: SearchResult
      :members:
      :undoc-members:
      :show-inheritance:
      :special-members: __repr__

      Search algorithm result container storing optimal solutions, objective values,
      runtime statistics, and convergence information.

