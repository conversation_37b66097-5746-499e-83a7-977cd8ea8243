#ifndef GRAPH_H
#define GRAPH_H
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

using Node = int;                          // Node identifier
using Age = int;                           // Node age
using NodeSet = std::unordered_set<Node>;  // Set of nodes
using ComponentIndex = int;                // Connected component index
using Solution = NodeSet;                  // Problem solution

/**
 * Component(
 *     size: int = 0,
 *     nodes: list[Node] = [],
 * )
 *
 * Represents a connected component in the graph.
 *
 * Parameters
 * ----------
 * size
 *     Number of nodes in this connected component. Default is 0.
 * nodes
 *     List of all nodes contained in this connected component. Default is empty list.
 *
 * Attributes
 * ----------
 * size
 *     Number of nodes in this connected component.
 * nodes
 *     List of all nodes contained in this connected component.
 */
struct Component
{
    size_t size;              ///< Number of nodes in this connected component.
    std::vector<Node> nodes;  ///< List of all nodes contained in this connected component.
    Component() noexcept : size(0) {}
};

using Components = std::vector<Component>;  // Set of connected components

/**
 * Graph
 *
 * Abstract base class for graph structures.
 *
 * Defines common interfaces for all graph operations. This class serves as a common base
 * for various concrete graph problem implementations (e.g., CNP, DCNP, NWCNP).
 * It declares a series of pure virtual functions (must be implemented by derived classes)
 * and virtual functions (default implementations that can be overridden by derived classes),
 * covering methods for graph construction, modification, querying, and specific algorithm requirements.
 */
class Graph
{
public:
    /**
     * Special value for invalid or non-existent nodes.
     *
     * This constant can be used in various graph operations when a node is not found,
     * not applicable, or invalid.
     */
    static constexpr Node INVALID_NODE = -1;

    /**
     * Graph default constructor.
     *
     * As an abstract base class, its constructor is typically simple,
     * mainly used for initializing the base class part.
     */
    Graph() = default;

    /**
     * Graph virtual destructor.
     *
     * Declared as virtual to ensure that when deleting a derived class object through
     * a base class pointer, the derived class's destructor is correctly called,
     * thus avoiding resource leaks.
     * `= default` indicates using the compiler-generated default destructor implementation.
     */
    virtual ~Graph() = default;

    /**
     * Create a deep copy of the current graph object.
     *
     * This is a pure virtual function. Derived classes must implement this method
     * to return a smart pointer to a new graph object of the same type and with
     * identical content. This is important for saving graph states or creating
     * multiple independent graph instances in algorithms.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     A `std::unique_ptr` pointing to the newly created graph object.
     *     Returns a base class pointer for polymorphism, but the actual object
     *     should be of the concrete derived class type.
     */
    virtual std::unique_ptr<Graph> clone() const = 0;

    /**
     * Update the current graph's state based on a set of nodes to be removed.
     *
     * This is a pure virtual function. Derived classes need to implement this method
     * to reflect changes in graph structure after removing the specified node set,
     * such as updating adjacency relationships and connectivity information.
     *
     * Parameters
     * ----------
     * nodesToRemove
     *     A constant reference to a `NodeSet` containing IDs of nodes to be removed.
     */
    virtual void updateGraphByRemovedNodes(const NodeSet &nodesToRemove) = 0;

    /**
     * Permanently remove a single specified node from the graph.
     *
     * This is a pure virtual function. Derived classes should implement specific
     * node removal logic, including updating the graph's internal representation
     * (such as adjacency lists, node lists) and related graph attributes.
     *
     * Parameters
     * ----------
     * nodeToRemove
     *     ID of the node to be removed from the graph.
     */
    virtual void removeNode(Node nodeToRemove) = 0;

    /**
     * Add (or restore) a single specified node to the graph.
     *
     * This is a pure virtual function. Derived classes should implement specific
     * node addition logic, which may include re-adding it to the node list,
     * restoring its adjacency relationships, etc.
     *
     * Parameters
     * ----------
     * nodeToAdd
     *     ID of the node to be added to the graph.
     */
    virtual void addNode(Node nodeToAdd) = 0;

    /**
     * Set the "age" attribute for a specific node in the graph.
     *
     * This is a pure virtual function. Node age can be used in certain heuristic
     * algorithms, such as marking the duration of a node's tabu status in tabu search,
     * or in age-based selection strategies.
     *
     * Parameters
     * ----------
     * node
     *     ID of the node whose age is to be set.
     * age
     *     Age value to be set for this node.
     */
    virtual void setNodeAge(Node node, Age age) = 0;

    /**
     * Get the objective function value for the current graph state.
     *
     * This is a pure virtual function. Derived classes must implement objective
     * function calculation logic based on their specific problem type
     * (e.g., CNP, DCNP, NWCNP).
     *
     * Returns
     * -------
     * int
     *     Returns the objective function value corresponding to the current graph configuration.
     */
    virtual int getObjectiveValue() const = 0;

    /**
     * Generate and return a random feasible instance of the current graph.
     *
     * This is a pure virtual function. Derived classes need to generate a new graph
     * state (possibly by modifying a copy of the current graph or creating from scratch)
     * that is a random valid solution or configuration under the problem constraints.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     A smart pointer to the newly generated random feasible graph instance.
     */
    virtual std::unique_ptr<Graph> getRandomFeasibleGraph() = 0;

    /**
     * Get a "reduced" graph based on a set of nodes to be removed.
     *
     * This is a pure virtual function. Unlike `updateGraphByRemovedNodes` which may
     * permanently modify the current graph, this method is typically used to create
     * a temporary, reduced-size version of the graph where specified nodes have been
     * removed. This may be useful in certain divide-and-conquer algorithms or
     * subproblem solving. Specific implementation depends on the derived class.
     *
     * Parameters
     * ----------
     * nodesToRemove
     *     A constant reference to a `NodeSet` containing IDs of nodes to be removed.
     */
    virtual void getReducedGraphByRemovedNodes(const NodeSet &nodesToRemove) = 0;

    /**
     * Check if a specified node is currently in a "removed" state.
     *
     * This is a pure virtual function. Derived classes need to maintain information
     * about removed nodes and return results accordingly.
     *
     * Parameters
     * ----------
     * node
     *     ID of the node whose removal status is to be checked.
     *
     * Returns
     * -------
     * bool
     *     Returns `true` if the node has been removed; otherwise returns `false`.
     */
    virtual bool isNodeRemoved(Node node) const = 0;

    /**
     * Get the set of all nodes currently marked as "removed" in the graph.
     *
     * This is a pure virtual function. Derived classes should return their
     * internally recorded set of removed nodes.
     *
     * Returns
     * -------
     * const NodeSet&
     *     A constant reference to the `NodeSet` containing IDs of all currently removed nodes.
     *     返回常量引用以避免不必要的拷贝，提高性能。
     */
    virtual const NodeSet& getRemovedNodes() const = 0;

    /**
     * Get the total number of nodes in the graph (usually referring to the original
     * graph or current valid graph).
     *
     * This is a pure virtual function. Derived classes should return the total
     * number of nodes in their represented graph.
     *
     * Returns
     * -------
     * int
     *     Total number of nodes in the graph.
     */
    virtual int getNumNodes() const = 0;

    // CNP (Critical Node Problem) and NWCNP (Node-Weighted Critical Node Problem)
    // specific methods. Default implementations provided as not all graph types need them.
    /**
     * (CNP/NWCNP) Select a connected component to be removed.
     *
     * This is a virtual method, mainly used in CNP and NWCNP. Default implementation
     * returns 0 (or an invalid index). Derived classes can override this method to
     * implement specific connected component selection strategies, such as selecting
     * the largest, smallest, or based on other heuristics.
     *
     * Returns
     * -------
     * ComponentIndex
     *     Index of the selected connected component. Default returns 0.
     */
    virtual ComponentIndex selectRemovedComponent() const { return 0; }

    /**
     * (CNP/NWCNP) Randomly select a node from a specified connected component.
     *
     * This is a virtual method. Default implementation returns `INVALID_NODE`.
     * CNP or NWCNP derived classes can override it to randomly select a node from
     * the connected component with the given index for operations (such as removal).
     *
     * Parameters
     * ----------
     * componentIndex
     *     Index of the target connected component.
     *
     * Returns
     * -------
     * Node
     *     ID of the randomly selected node. Default returns `INVALID_NODE`.
     */
    virtual Node
    randomSelectNodeFromComponent(ComponentIndex componentIndex) const
    {
        (void)componentIndex; // 避免未使用参数警告
        return INVALID_NODE;
    }

    /**
     * (CNP/NWCNP) Select a node from a specified connected component that has the
     * minimum impact (or optimal according to some heuristic) when removed.
     *
     * This is a virtual method. Default implementation returns `INVALID_NODE`.
     * Derived classes can implement a strategy to select a node from the given
     * connected component that has the minimum impact on the objective function
     * (or other evaluation metrics) when removed.
     *
     * Parameters
     * ----------
     * componentIndex
     *     Index of the target connected component.
     *
     * Returns
     * -------
     * Node
     *     ID of the node selected based on impact evaluation. Default returns `INVALID_NODE`.
     */
    virtual Node
    impactSelectNodeFromComponent(ComponentIndex componentIndex) const
    {
        (void)componentIndex; // 避免未使用参数警告
        return INVALID_NODE;
    }

    /**
     * (CNP/NWCNP) Select a node from a specified connected component based on its "age".
     *
     * This is a virtual method. Default implementation returns `INVALID_NODE`.
     * Derived classes can implement a selection strategy based on node age attributes,
     * such as preferring older or younger nodes.
     *
     * Parameters
     * ----------
     * componentIndex
     *     Index of the target connected component.
     *
     * Returns
     * -------
     * Node
     *     ID of the node selected based on age. Default returns `INVALID_NODE`.
     */
    virtual Node ageSelectNodeFromComponent(ComponentIndex componentIndex) const
    {
        (void)componentIndex; // 避免未使用参数警告
        return INVALID_NODE;
    }

    /**
     * (CNP/NWCNP) Greedily select a removed node to add back to the graph.
     *
     * This is a virtual method. Default implementation returns `INVALID_NODE`.
     * Derived classes can implement a greedy strategy to select a node from removed
     * nodes that would most improve the objective function (or other metrics) when
     * added back to the graph.
     *
     * Returns
     * -------
     * Node
     *     ID of the node greedily selected to be added back to the graph. Default returns `INVALID_NODE`.
     */
    virtual Node greedySelectNodeToAdd() const { return INVALID_NODE; }

    /**
     * (CNP/NWCNP) Randomly select a node from the graph (usually from removable candidates)
     * for removal.
     *
     * This is a virtual method. Default implementation returns `INVALID_NODE`.
     * Derived classes can implement a simple random selection strategy to pick a node
     * for removal operations.
     *
     * Returns
     * -------
     * Node
     *     ID of the randomly selected node for removal. Default returns `INVALID_NODE`.
     */
    virtual Node randomSelectNodeToRemove() const { return INVALID_NODE; }

    /**
     * (CNP/NWCNP) Calculate the connectivity gain if a specified node is added back
     * to the graph (or changed from removed to active state).
     *
     * This is a virtual method. Default implementation returns 0.
     * Derived classes can implement calculation logic to evaluate the contribution
     * of a specific node to graph connectivity (e.g., number of connected pairs)
     * when added or activated.
     *
     * Parameters
     * ----------
     * node
     *     ID of the node being considered for addition or activation.
     *
     * Returns
     * -------
     * int
     *     Represents the connectivity gain due to adding this node (specific measure
     *     depends on the problem). Default returns 0.
     */
    virtual int calculateConnectionGain(Node node) const {
        (void)node; // 避免未使用参数警告
        return 0;
    }

    // DCNP (Distance-based Node Problem) specific methods.
    // Default implementations provided as not all graph types need them.
    /**
     * (DCNP) Build K-hop tree structure.
     *
     * This is a virtual method, mainly used in DCNP. Default implementation is a no-op.
     * DCNP derived classes need to implement this method to build or update K-hop
     * reachability trees for all relevant nodes based on the current graph state and K value.
     */
    virtual void build_tree() {}

    /**
     * (DCNP) Calculate the total size (or some aggregate measure) of K-hop trees
     * for all nodes in the current graph.
     *
     * This is a virtual method. Default implementation returns 0.
     * DCNP derived classes need to implement this method to calculate their objective
     * function, which is typically related to K-hop reachability.
     *
     * Returns
     * -------
     * int
     *     Total size of K-hop trees or related measure. Default returns 0.
     */
    virtual int calculateKHopTreeSize() const { return 0; }

    /**
     * (DCNP) Calculate betweenness centrality for nodes in the graph.
     *
     * This is a virtual method. Default implementation returns a reference to an
     * empty static vector. DCNP derived classes can implement betweenness centrality
     * calculation and return a vector containing centrality values for each node.
     * Betweenness centrality can be used as a heuristic for selecting nodes to remove.
     *
     * Returns
     * -------
     * const std::vector<double>&
     *     Constant reference to a vector containing betweenness centrality values
     *     for each node. Default returns a static empty vector.
     */
    virtual const std::vector<double> &calculateBetweennessCentrality() const
    {
        static std::vector<double>
            empty;  // static to ensure its lifetime for the reference
        return empty;
    }

    /**
     * (DCNP) Find the "best" node to remove from the current graph based on some strategy.
     *
     * This is a virtual method. Default implementation returns `INVALID_NODE`.
     * DCNP derived classes can implement a heuristic (e.g., based on evaluation of
     * impact on objective function, betweenness centrality, etc.) to select the next
     * node that should be removed.
     *
     * Returns
     * -------
     * Node
     *     ID of the node selected as the best removal candidate. Default returns `INVALID_NODE`.
     */
    virtual Node findBestNodeToRemove() { return INVALID_NODE; }

    /**
     * (DCNP) Find the "best" node to add back to the graph from removed nodes based
     * on some strategy.
     *
     * This is a virtual method. Default implementation returns `INVALID_NODE`.
     * DCNP derived classes can implement a heuristic to select a node from removed
     * nodes that would most improve the objective function when added back to the graph.
     *
     * Returns
     * -------
     * Node
     *     ID of the node selected as the best addition candidate. Default returns `INVALID_NODE`.
     */
    virtual Node findBestNodeToAdd() { return INVALID_NODE; }
};

#endif  // GRAPH_H