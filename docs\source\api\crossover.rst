.. module:: pycnp._pycnp
   :synopsis: Crossover operators and utilities for evolutionary algorithms

Crossover Operators
====================

The :class:`~pycnp._pycnp.Crossover` class provides multiple crossover operators
designed specifically for critical node problems. These operators effectively combine
features from parent solutions to generate high-quality offspring in evolutionary algorithms.

.. autoclass:: pycnp._pycnp.Crossover
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

   The Crossover class implements problem-specific crossover operators that leverage
   the structure of critical node problems to produce meaningful offspring solutions.

Crossover Operator Types
------------------------

PyCNP provides three main types of crossover operators, each with distinct characteristics:

Double Backbone Crossover (DB)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pycnp._pycnp.Crossover.double_backbone_based_crossover

   Generates offspring solutions based on the common backbone and unique nodes of two parent solutions.

   **Algorithm Steps:**

   - Identifies common nodes (backbone) between two parent solutions
   - Analyzes unique nodes specific to each parent
   - Combines features using heuristic rules to generate new solutions

   **Use Cases:** Suitable for most CNP problems, providing a good balance between exploration and exploitation.

   **Parameters:**

   - ``graph``: The problem graph instance
   - ``parents``: List containing exactly two parent solutions (sets of node IDs)

   **Returns:** New graph instance representing the offspring solution

Reduce-Solve-Combine Crossover (RSC)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pycnp._pycnp.Crossover.reduce_solve_combine

   Generates offspring solutions through problem reduction and re-solving strategies.

   **Algorithm Steps:**

   - Reduces the original problem into smaller, manageable subproblems
   - Applies exact or approximate solving techniques to subproblems
   - Combines subproblem solutions to form a complete solution for the original problem

   **Use Cases:** Particularly effective for highly structured graphs where decomposition
   can lead to high-quality solutions.

   **Parameters:**

   - ``graph``: The problem graph instance
   - ``parents``: List containing exactly two parent solutions

   **Returns:** New graph instance with optimized node selection

Inherit-Repair-Recombination Crossover (IRR)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. automethod:: pycnp._pycnp.Crossover.inherit_repair_recombination

   Advanced three-parent crossover operator that generates offspring based on common
   backbone structures from multiple parent solutions.

   **Algorithm Steps:**

   - Analyzes common features across three parent solutions
   - Identifies multiple backbone structures and inheritance patterns
   - Applies repair and recombination mechanisms to generate stable offspring

   **Use Cases:** Ideal for populations with high diversity, effectively combining
   advantages from multiple solutions.

   **Parameters:**

   - ``graph``: The problem graph instance
   - ``parents``: List containing exactly three parent solutions

   **Returns:** New graph instance combining features from all three parents

Usage Examples
--------------

Basic Crossover Operations
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from pycnp._pycnp import Crossover
   import pycnp

   # Load problem data and create graph
   problem_data = pycnp.read("graph.txt")
   graph = problem_data.create_graph("CNP")

   # Create crossover operator instance
   crossover = Crossover(seed=42)

   # Prepare parent solutions (sets of node IDs to remove)
   parent1 = {1, 3, 5, 7, 9}
   parent2 = {2, 4, 6, 8, 10}
   parents = [parent1, parent2]

   # Execute double backbone crossover
   offspring_graph = crossover.double_backbone_based_crossover(
       graph, parents
   )

   # For three-parent crossover, prepare three parents
   parent3 = {1, 2, 11, 12, 13}
   three_parents = [parent1, parent2, parent3]

   # Execute inherit-repair-recombination crossover
   offspring_graph = crossover.inherit_repair_recombination(
       graph, three_parents
   )

Advanced Usage with Parameter Tuning
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Create multiple crossover instances with different seeds
   crossovers = [Crossover(seed=i) for i in range(10)]

   # Test different crossover operators
   operators = [
       "double_backbone_based_crossover",
       "reduce_solve_combine",
       "inherit_repair_recombination"
   ]

   best_offspring = None
   best_objective = float('inf')

   for crossover in crossovers:
       for op_name in operators:
           if op_name == "inherit_repair_recombination":
               # Requires three parents
               if len(parent_pool) >= 3:
                   parents = parent_pool[:3]
                   offspring = getattr(crossover, op_name)(graph, parents)
           else:
               # Requires two parents
               parents = parent_pool[:2]
               offspring = getattr(crossover, op_name)(graph, parents)

           # Evaluate offspring quality
           objective = offspring.evaluate_objective()
           if objective < best_objective:
               best_objective = objective
               best_offspring = offspring

Error Handling
--------------

All crossover operators include comprehensive error handling mechanisms:

**Parameter Validation**
   - Verifies correct number of parent solutions for each operator
   - Validates graph instance compatibility
   - Checks solution feasibility and constraints

**Type Checking**
   - Ensures solution sets contain valid node IDs
   - Validates data types and ranges
   - Prevents invalid operations on graph structures

**Exception Propagation**
   - Provides clear and informative error messages
   - Maintains error context for debugging
   - Handles both C++ and Python exceptions gracefully

.. code-block:: python

   try:
       # Error: Double backbone crossover requires exactly 2 parents
       result = crossover.double_backbone_based_crossover(graph, [parent1])
   except InvalidArgumentError as e:
       print(f"Parameter error: {e}")

   try:
       # Error: Solution set contains invalid node IDs
       invalid_parent = {-1, "invalid_node"}
       result = crossover.double_backbone_based_crossover(
           graph, [parent1, invalid_parent]
       )
   except RuntimeError as e:
       print(f"Runtime error: {e}")

   try:
       # Error: Graph instance is incompatible
       wrong_graph_type = create_different_graph()
       result = crossover.reduce_solve_combine(wrong_graph_type, parents)
   except TypeError as e:
       print(f"Type error: {e}")

Performance Characteristics
---------------------------

**High-Performance Implementation**
   - All crossover operators implemented in optimized C++ code
   - Memory-efficient data structures and algorithms
   - Minimal Python-C++ interface overhead

**Scalability**
   - Supports large-scale graph instances (>10,000 nodes)
   - Linear time complexity for most operations
   - Efficient memory management with automatic cleanup

**Return Value Optimization**
   - Optimized return value policies to avoid unnecessary memory copies
   - Smart pointer usage for automatic memory management
   - Efficient data transfer between C++ and Python

Algorithm Selection Guidelines
------------------------------

**Double Backbone (DB)**
   - **Best for:** General-purpose applications, balanced exploration/exploitation
   - **Graph types:** Works well with most graph structures
   - **Population diversity:** Maintains good diversity with moderate computational cost

**Reduce-Solve-Combine (RSC)**
   - **Best for:** Highly structured graphs, hierarchical networks
   - **Graph types:** Graphs with clear community structure or modularity
   - **Solution quality:** Often produces high-quality solutions but with higher computational cost

**Inherit-Repair-Recombination (IRR)**
   - **Best for:** Diverse populations, multi-objective scenarios
   - **Graph types:** Complex networks where multiple solution features are important
   - **Convergence:** Helps maintain population diversity and prevents premature convergence

.. tip::

   The choice of crossover operator should be based on problem characteristics and
   computational requirements. We recommend testing different operators on your
   specific problem instances to determine the most effective combination.

.. note::

   For optimal performance, consider using different crossover operators at different
   stages of the evolutionary process, or employ adaptive selection mechanisms based
   on population diversity and convergence metrics.
