.. module:: pycnp.stop
   :synopsis: Stopping criteria


Stopping criteria
=================

The :mod:`pycnp.stop` module contains the various stopping criteria the ``pycnp`` package ships with.
These can be used to stop the :class:`~pycnp.MemeticSearch.MemeticSearch`'s' search whenever some criterion is met: for example, when some maximum number of iterations or run-time is exceeded.

All stopping criteria implement the :class:`~pycnp.stop.StoppingCriterion.StoppingCriterion` protocol.

.. automodule:: pycnp.stop.StoppingCriterion

   .. autoclass:: StoppingCriterion
      :members:
      :special-members: __call__

.. automodule:: pycnp.stop.MaxIterations

   .. autoclass:: MaxIterations
      :members:

.. automodule:: pycnp.stop.MaxRuntime

   .. autoclass:: MaxRuntime
      :members:

.. automodule:: pycnp.stop.NoImprovement

   .. autoclass:: NoImprovement
      :members:
