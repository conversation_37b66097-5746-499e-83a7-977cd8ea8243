/**
 * @file pybind.cpp
 * @brief Python bindings for PyCNP - Critical Node Problem solver package
 * <AUTHOR>
 * @date 2024
 *
 * This file implements Python bindings for the PyCNP algorithm package, using pybind11
 * to expose C++ core algorithms to Python interface. Contains bindings for core components
 * including graph structures, crossover operators, population management, search strategies, etc.
 */

// Standard library headers
#include <stdexcept>
#include <memory>
#include <functional>

// PyCNP core headers
#include "Crossover.h"
#include "Graph/CNP_Graph.h"
#include "Graph/DCNP_Graph.h"
#include "Graph/Graph.h"
#include "Graph/NWCNP_Graph.h"
#include "Population.h"
#include "ProblemData.h"
#include "search/Search.h"

// pybind11 related headers
#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/stl_bind.h>
#include <pybind11/numpy.h>

// Auto-generated documentation headers
#include "pycnp_autogen_docs.h"

namespace py = pybind11;

// ============================================================================
// Helper function definitions
// ============================================================================

/**
 * @brief Convert C++ Solution (unordered_set<int>) to Python set
 *
 * This helper function is used to convert solution set data structures between C++ and Python.
 * Solution is represented as unordered_set<int> in C++, containing IDs of removed nodes.
 *
 * @param sol C++ solution set containing node IDs
 * @return Python set object containing the same node IDs
 * @throws std::bad_alloc if memory allocation fails
 */
py::set solution_to_pyset(const Solution &sol)
{
    try {
        py::set pyset;
        for (const auto &node : sol) {
            pyset.add(py::int_(node));
        }
        return pyset;
    } catch (const std::exception& e) {
        throw std::runtime_error("Failed to convert C++ Solution to Python set: " + std::string(e.what()));
    }
}

/**
 * @brief Convert Python set to C++ Solution (unordered_set<int>)
 *
 * This helper function converts Python-passed node sets to C++ Solution type.
 * Includes input validation to ensure all elements are valid integer node IDs.
 *
 * @param py_set Python set object, should contain integer node IDs
 * @return C++ Solution object
 * @throws std::invalid_argument if set contains non-integer elements
 * @throws std::runtime_error if error occurs during conversion
 */
Solution pyset_to_solution(const py::set &py_set)
{
    try {
        Solution sol;
        for (auto item : py_set) {
            if (!py::isinstance<py::int_>(item)) {
                throw std::invalid_argument("All elements in solution set must be integers");
            }
            int node_id = item.cast<int>();
            if (node_id < 0) {
                throw std::invalid_argument("Node IDs must be non-negative integers");
            }
            sol.insert(node_id);
        }
        return sol;
    } catch (const py::cast_error& e) {
        throw std::invalid_argument("Failed to cast Python set element to integer: " + std::string(e.what()));
    } catch (const std::exception& e) {
        throw std::runtime_error("Failed to convert Python set to C++ Solution: " + std::string(e.what()));
    }
}

// ============================================================================
// Python module definition
// ============================================================================

PYBIND11_MODULE(_pycnp, m)
{
    // Module documentation string
    m.doc() = R"doc(
        PyCNP - Python bindings for Critical Node Problem solver

        This module provides Python bindings for a high-performance C++ implementation
        of algorithms for solving Critical Node Problems (CNP), Distance-based
        Critical Node Problems (DCNP), and Node-Weighted Critical Node Problems (NWCNP).

        The module includes:
        - Graph data structures and algorithms
        - Population-based search algorithms
        - Crossover operators for evolutionary algorithms
        - Local search strategies
        - Problem data management utilities

        Example:
            >>> import pycnp
            >>> problem_data = pycnp.read("graph.txt")
            >>> model = pycnp.Model.from_data(problem_data)
            >>> result = model.solve("CNP", bound=10, stopping_criterion=pycnp.MaxRuntime(60))
    )doc";

    // ========================================================================
    // Constant definitions - Search strategies
    // ========================================================================

    // Search strategy constants - used to specify local search algorithm types
    m.attr("CBNS") = "CBNS";  // Component-Based Neighborhood Search
    m.attr("CHNS") = "CHNS";  // Component-Based Hybrid Neighborhood Search
    m.attr("DLAS") = "DLAS";  // Diversified Late Acceptance Search
    m.attr("BCLS") = "BCLS";  // Betweenness centrality-based late-acceptance search

    // Problem type constants - used to specify the problem type to solve
    m.attr("CNP") = "CNP";     // Critical Node Problem
    m.attr("DCNP") = "DCNP";   // Distance-based Critical Node Problem
    m.attr("NWCNP") = "NWCNP"; // Node-Weighted Critical Node Problem

    // Crossover strategy constants - used to specify crossover operator types
    m.attr("DBX") = "DBX";     // Double Backbone Based Crossover
    m.attr("RSC") = "RSC";     // Reduce Solve Combine
    m.attr("IRR") = "IRR";     // Inherit Repair Recombination Crossover

    // ========================================================================
    // Exception type bindings
    // ========================================================================

    // Bind standard exception types to ensure C++ exceptions are properly passed to Python
    // These exceptions will automatically map to corresponding Python exception types
    py::register_exception<std::invalid_argument>(m, "InvalidArgumentError");
    py::register_exception<std::runtime_error>(m, "RuntimeError");
    py::register_exception<std::out_of_range>(m, "OutOfRangeError");
    py::register_exception<std::logic_error>(m, "LogicError");
    py::register_exception<std::bad_alloc>(m, "MemoryError");

    // ========================================================================
    // Graph structure class bindings
    // ========================================================================

    // Graph base class binding - only as base class, no specific methods bound
    // Note: Use py::nodelete to prevent Python from deleting C++-managed objects
    py::class_<Graph, std::unique_ptr<Graph, py::nodelete>>(
        m, "Graph", D(Graph));

    // CNP_Graph class binding - Standard Critical Node Problem
    py::class_<CNP_Graph, Graph, std::unique_ptr<CNP_Graph, py::nodelete>>(
        m, "CNP_Graph", D(CNP_Graph))
        .def(py::init<>(), D(CNP_Graph, CNP_Graph))
        .def(py::init<NodeSet, std::vector<NodeSet>, int, int>(),
             py::arg("nodes"), py::arg("adj_list"), py::arg("bound"), py::arg("seed"))

        // Important methods for graph structure operations
        .def_readonly("removed_nodes", &CNP_Graph::removedNodes)
        .def("remove_node", &CNP_Graph::removeNode,
             py::arg("node_to_remove"))
        .def("add_node", &CNP_Graph::addNode,
             py::arg("node_to_add"));

    // DCNP_Graph class binding - Distance-Constrained Critical Node Problem
    py::class_<DCNP_Graph, Graph, std::unique_ptr<DCNP_Graph, py::nodelete>>(
        m, "DCNP_Graph", D(DCNP_Graph))
        .def(py::init<>(), D(DCNP_Graph, DCNP_Graph))
        .def(py::init<NodeSet, int, std::vector<NodeSet>, int, int>(),
             py::arg("nodes"), py::arg("K"), py::arg("adj_list"), py::arg("num_to_remove"), py::arg("seed"))

        // Important methods for graph structure operations
        .def("calculate_khop_tree_size",
             py::overload_cast<>(&DCNP_Graph::calculateKHopTreeSize, py::const_))
        .def("build_tree", &DCNP_Graph::build_tree)
        .def("remove_node", &DCNP_Graph::removeNode,
             py::arg("node_to_remove"))
        .def("add_node", &DCNP_Graph::addNode,
             py::arg("node_to_add"));

    // NWCNP_Graph class binding - Node-Weighted Critical Node Problem
    py::class_<NWCNP_Graph, Graph, std::unique_ptr<NWCNP_Graph, py::nodelete>>(
        m, "NWCNP_Graph", D(NWCNP_Graph))
        .def(py::init<>(), D(NWCNP_Graph, NWCNP_Graph))
        .def(py::init<NodeSet, std::unordered_map<int, double>, std::vector<NodeSet>, double, int>(),
            py::arg("nodes"), py::arg("node_weights"), py::arg("adj_list"), py::arg("bound"), py::arg("seed"))

        // Key attributes
        .def_readwrite("bound", &NWCNP_Graph::bound)
        .def_readwrite("sol_weight", &NWCNP_Graph::SolWeight)
        .def_readonly("removed_nodes", &NWCNP_Graph::removedNodes)

        // Important methods for graph structure operations
        .def("remove_node", &NWCNP_Graph::removeNode,
            py::arg("node_to_remove"))
        .def("add_node", &NWCNP_Graph::addNode,
            py::arg("node_to_add"));
    // ========================================================================
    // Crossover operator class bindings
    // ========================================================================

    // Crossover class binding - Provides multiple crossover operators for evolutionary algorithms
    py::class_<Crossover, std::shared_ptr<Crossover>>(
        m, "Crossover", D(Crossover))
        .def(py::init<int>(),
            py::arg("seed"),
            D(Crossover, Crossover))

        .def("double_backbone_based_crossover",
            [](Crossover& self, const Graph& orig_graph, const std::vector<py::set>& parents) {
                std::vector<Solution> solutions;
                solutions.reserve(2);

                for (const auto& p_set : parents) {
                    solutions.push_back(pyset_to_solution(p_set));
                }
                std::pair<const Solution*, const Solution*> parent_pair = {&solutions[0], &solutions[1]};
                return self.doubleBackboneBasedCrossover(orig_graph, parent_pair);
            },
            py::arg("orig_graph"),
            py::arg("parents"),
            py::return_value_policy::take_ownership,
            D(Crossover, doubleBackboneBasedCrossover))

        .def("reduce_solve_combine",
             [](Crossover &self, const Graph &orig_graph, const std::vector<py::set> &py_parents) {
                 // Parameter validation
                 if (py_parents.size() != 2) {
                     throw std::invalid_argument("Reduce-solve-combine crossover requires exactly 2 parent solutions");
                 }

                 try {
                     // Convert Python set to C++ Solution type
                     std::vector<Solution> cpp_solutions;
                     cpp_solutions.reserve(2);

                     for (const auto &p_set : py_parents) {
                         cpp_solutions.push_back(pyset_to_solution(p_set));
                     }

                     std::pair<const Solution *, const Solution *> parent_pair = {&cpp_solutions[0], &cpp_solutions[1]};
                     return self.reduceSolveCombine(orig_graph, parent_pair);
                 } catch (const std::exception& e) {
                     throw std::runtime_error("Reduce-solve-combine crossover failed: " + std::string(e.what()));
                 }
             },
             py::arg("orig_graph"),
             py::arg("parents"),
             py::return_value_policy::take_ownership,
             D(Crossover, reduceSolveCombine))
        .def("inherit_repair_recombination",
             [](Crossover &self, const Graph &orig_graph, const std::vector<py::set> &py_parents) {
                 // Parameter validation
                 if (py_parents.size() != 3) {
                     throw std::invalid_argument("Inherit repair recombination crossover requires exactly 3 parent solutions");
                 }

                 try {
                     // Convert Python set to C++ Solution type
                     std::vector<Solution> cpp_solutions;
                     cpp_solutions.reserve(3);

                     for (const auto &p_set : py_parents) {
                         cpp_solutions.push_back(pyset_to_solution(p_set));
                     }

                     // Create parent tuple
                     std::tuple<const Solution *, const Solution *, const Solution *> parent_tuple = {
                         &cpp_solutions[0], &cpp_solutions[1], &cpp_solutions[2]
                     };

                     return self.inherit_repair_recombination(orig_graph, parent_tuple);
                 } catch (const std::exception& e) {
                     throw std::runtime_error("Inherit repair recombination crossover failed: " + std::string(e.what()));
                 }
             },
             py::arg("orig_graph"),
             py::arg("parents"),
             py::return_value_policy::take_ownership,
             D(Crossover, inherit_repair_recombination));

    // ========================================================================
    // Parameter configuration class bindings
    // ========================================================================

    // MSParams class binding - Memetic search parameter configuration
    py::class_<MSParams>(m, "MSParams", D(MSParams))
        .def(py::init<>())
        .def(py::init<std::string, std::string, bool, int>(),
             py::arg("search") = "CHNS",
             py::arg("crossover") = "RSC",
             py::arg("is_pop_variable") = true,
             py::arg("initial_pop_size") = 5)
        .def_readwrite("search", &MSParams::search)
        .def_readwrite("crossover", &MSParams::crossover)
        .def_readwrite("is_pop_variable", &MSParams::is_pop_variable)
        .def_readwrite("initial_pop_size", &MSParams::initial_pop_size)
        .def("__repr__", [](const MSParams &p) {
            return "<MSParams(search='" + p.search +
                   "', crossover='" + p.crossover +
                   "', is_pop_variable=" + (p.is_pop_variable ? "True" : "False") +
                   ", initial_pop_size=" + std::to_string(p.initial_pop_size) + ")>";
        });

    // VPParams class binding - Variable population parameter configuration
    py::class_<VPParams>(m, "VPParams", D(VPParams))
        .def(py::init<>())
        .def(py::init<int, int, int>(),
            py::arg("max_pop_size") = 20,
            py::arg("increase_pop_size") = 3,
            py::arg("max_idle_gens") = 20)
        .def_readwrite("max_pop_size", &VPParams::maxPopSize)
        .def_readwrite("increase_pop_size", &VPParams::increasePopSize)
        .def_readwrite("max_idle_gens", &VPParams::maxIdleGens)
        .def("__repr__", [](const VPParams &p) {
            return "<VPParams(max_pop_size=" + std::to_string(p.maxPopSize) +
                  ", increase_pop_size=" + std::to_string(p.increasePopSize) +
               ", max_idle_gens=" + std::to_string(p.maxIdleGens) + ")>";
        });

    // ========================================================================
    // Search algorithm class bindings
    // ========================================================================

    // Search class binding - Local search algorithm manager
    py::class_<Search>(m, "Search", D(Search))
        .def(py::init<Graph &, int>(),
             py::arg("graph"),
             py::arg("seed"),
             D(Search, Search))
        .def("set_strategy", &Search::setStrategy,
             py::arg("strategy"),
             D(Search, setStrategy))
        .def("run", &Search::run,
             D(Search, run));

    // ========================================================================
    // Problem data class bindings
    // ========================================================================

    // ProblemData class binding - Graph problem data manager
    py::class_<ProblemData>(m, "ProblemData", D(ProblemData))
        .def(py::init<int>(),
             py::arg("num_nodes"),
             D(ProblemData, ProblemData))
        .def_static("read_from_adjacency_list_file",
                    &ProblemData::readFromAdjacencyListFile,
                    py::arg("filename"),
                    D(ProblemData, readFromAdjacencyListFile))
        .def_static("read_from_edge_list_format",
                    &ProblemData::readFromEdgeListFormat,
                    py::arg("filename"),
                    D(ProblemData, readFromEdgeListFormat))
        .def("read_node_weights_from_file",
             &ProblemData::readNodeWeightsFromFile,
             py::arg("filename"),
             D(ProblemData, readNodeWeightsFromFile))
        .def("create_original_graph",
             &ProblemData::createOriginalGraph,
             py::arg("problem_type"),
             py::arg("bound"),
             py::arg("seed"),
             py::arg("K") = std::numeric_limits<int>::max(),
             D(ProblemData, createOriginalGraph),
             py::return_value_policy::take_ownership)
        .def("add_node", &ProblemData::addNode,
             py::arg("node_id"),
             D(ProblemData, addNode))
        .def("add_edge", &ProblemData::addEdge,
             py::arg("u"), py::arg("v"),
             D(ProblemData, addEdge))
        .def("add_node_weight", &ProblemData::addNodeWeight,
             py::arg("node_id"), py::arg("weight"),
             D(ProblemData, addNodeWeight))
        .def("get_node_weight", &ProblemData::getNodeWeight,
             py::arg("node_id"),
             D(ProblemData, getNodeWeight))
        .def("get_nodes_set", &ProblemData::getNodesSet,
             py::return_value_policy::reference_internal)
        .def("get_adj_list", &ProblemData::getAdjList,
             py::return_value_policy::reference_internal)
        .def("num_nodes", &ProblemData::numNodes);


    // ========================================================================
    // Population management class bindings
    // ========================================================================

    // Population class binding - Evolutionary algorithm population manager
    py::class_<Population, std::unique_ptr<Population>>(m, "Population", D(Population))
        .def(py::init<Graph &, const MSParams &, const VPParams &, int>(),
             py::arg("original_graph"),
             py::arg("memetic_search_params"),
             py::arg("variable_population_params"),
             py::arg("seed"))
        .def("update",
             [](Population &self, const py::set &solution_set, int obj_value,
                int num_idle_generations, bool verbose) {
                 try {
                     // Parameter validation
                     if (obj_value < 0) {
                         throw std::invalid_argument("Objective value must be non-negative");
                     }
                     if (num_idle_generations < 0) {
                         throw std::invalid_argument("Number of idle generations must be non-negative");
                     }

                     // Convert solution set
                     Solution sol = pyset_to_solution(solution_set);
                     self.update(sol, obj_value, num_idle_generations, verbose);
                 } catch (const std::exception& e) {
                     throw std::runtime_error("Population update failed: " + std::string(e.what()));
                 }
             },
             py::arg("solution"),
             py::arg("obj_value"),
             py::arg("num_idle_generations"),
             py::arg("verbose") = false,
             D(Population, update))
        .def("initialize",
             [](Population &self, bool display, py::object stopping_criterion_obj) {
                 try {
                     // Build stopping criterion function
                     std::function<bool(int)> stopping_criterion = nullptr;
                     if (!stopping_criterion_obj.is_none()) {
                         // Verify if stopping criterion object is callable
                         if (!py::hasattr(stopping_criterion_obj, "__call__")) {
                             throw std::invalid_argument("Stopping criterion must be callable");
                         }

                         stopping_criterion = [stopping_criterion_obj](int best_obj_value) -> bool {
                             try {
                                 return stopping_criterion_obj(best_obj_value).cast<bool>();
                             } catch (const py::cast_error& e) {
                                 throw std::runtime_error("Stopping criterion must return boolean value");
                             }
                         };
                     }

                     auto [solution, obj_value] = self.initialize(display, stopping_criterion);
                     return std::make_pair(solution_to_pyset(solution), obj_value);
                 } catch (const std::exception& e) {
                     throw std::runtime_error("Population initialization failed: " + std::string(e.what()));
                 }
             },
             py::arg("display") = false,
             py::arg("stopping_criterion") = py::none())
        .def("add",
             [](Population &self, const py::set &solution_set, int obj_value) {
                 try {
                     // Parameter validation
                     if (obj_value < 0) {
                         throw std::invalid_argument("Objective value must be non-negative");
                     }

                     Solution sol = pyset_to_solution(solution_set);
                     self.add(sol, obj_value);
                 } catch (const std::exception& e) {
                     throw std::runtime_error("Failed to add solution to population: " + std::string(e.what()));
                 }
             },
             py::arg("solution"),
             py::arg("obj_value"),
             D(Population, add))
        .def("select",
             [](Population &p) {
                 try {
                     // Tournament selection of two different solutions as parents
                     auto result = p.tournamentSelectTwoSolutions();
                     py::tuple py_result(2);
                     py_result[0] = solution_to_pyset(result.first);
                     py_result[1] = solution_to_pyset(result.second);
                     return py_result;
                 } catch (const std::exception& e) {
                     throw std::runtime_error("Solution selection failed: " + std::string(e.what()));
                 }
             },
             D(Population, tournamentSelectTwoSolutions))
        .def("get_all_three_solutions",
             [](const Population &p) {
                 try {
                     auto [sol1, sol2, sol3] = p.getAllThreeSolutions();
                     py::tuple py_result(3);
                     py_result[0] = solution_to_pyset(sol1);
                     py_result[1] = solution_to_pyset(sol2);
                     py_result[2] = solution_to_pyset(sol3);
                     return py_result;
                 } catch (const std::exception& e) {
                     throw std::runtime_error("Failed to get three solutions: " + std::string(e.what()));
                 }
             },
             D(Population, getAllThreeSolutions))
        .def("is_duplicate", &Population::isDuplicate,
             py::arg("solution"),
             D(Population, isDuplicate))
        .def("expand", &Population::expand,
             D(Population, expand))
        .def("rebuild", &Population::rebuild,
             D(Population, rebuild))
        .def("get_size", &Population::getSize,
             D(Population, isDuplicate_size));

    // ========================================================================
    // Search result class bindings
    // ========================================================================

    // SearchResult class binding - Search algorithm result container
    py::class_<SearchResult>(m, "SearchResult", D(SearchResult))
        .def(py::init<>())
        .def_property_readonly("solution",
                              [](const SearchResult &r) {
                                  try {
                                      return solution_to_pyset(r.solution);
                                  } catch (const std::exception& e) {
                                      throw std::runtime_error("Failed to convert solution: " + std::string(e.what()));
                                  }
                              })
        .def_readwrite("obj_value", &SearchResult::objValue)
        .def("__repr__", [](const SearchResult &r) {
            return "<SearchResult(obj_value=" + std::to_string(r.objValue) +
                   ", solution_size=" + std::to_string(r.solution.size()) + ")>";
        });

    // ========================================================================
    // Module end marker
    // ========================================================================
}