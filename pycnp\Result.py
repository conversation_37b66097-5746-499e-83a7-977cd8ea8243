import math
from dataclasses import dataclass, field
from typing import Set, Any, Dict, Optional

# Forward declaration for type hinting
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .Statistics import Statistics

@dataclass
class Result:
    """
    Stores the outcomes of a single run. An instance of this class is returned
    once the algorithm completes.

    Parameters
    ----------
    best_solution : Set[int]
        The best observed solution, represented as a set of items/nodes.
        Defaults to an empty set via default_factory.
    best_obj_value : float
        The objective function value of the best solution.
        Defaults to positive infinity, indicating no feasible solution found or
        not yet evaluated.
    num_iterations : int
        Number of iterations performed by the algorithm. Defaults to 0.
    runtime : float
        Total runtime of the main algorithm loop, in seconds. Defaults to 0.0.
    stats : Optional['Statistics']
        A Statistics object containing performance statistics. Can be None if
        statistics collection was disabled. Defaults to None.

    Raises
    ------
    ValueError
        When the number of iterations or runtime are negative.
    """

    best_solution: Set[int] = field(default_factory=set)
    best_obj_value: float = math.inf
    num_iterations: int = 0
    runtime: float = 0.0
    stats: Optional['Statistics'] = None

    def __post_init__(self):
        if self.num_iterations < 0:
            raise ValueError("Negative number of iterations not understood.")

        if self.runtime < 0:
            raise ValueError("Negative runtime not understood.")

    def cost(self) -> float:
        """
        Returns the cost (objective) value of the best solution.
        """
        return self.best_obj_value


    def summary(self) -> str:
        """
        Returns a nicely formatted result summary.
        """
        obj_str = f"{self.cost():.2f}" 
        indent = "    "  # Standard indentation
        label_width = 20  # Width for labels to align colons

        summary_lines = [
            "Solution results",
            "================",
            f"{indent}{'nodes number in solution':<{label_width}}: {len(self.best_solution) if self.best_solution else 0}",
            f"{indent}{'objective':<{label_width}}: {obj_str}",
            f"{indent}{'iterations':<{label_width}}: {self.num_iterations}",
            f"{indent}{'run-time':<{label_width}}: {self.runtime:.2f} seconds",
        ]
        return "\n".join(summary_lines)

    def __str__(self) -> str:
        """
        Returns a string representation of the result, including a summary
        and the best solution found.
        """
        solution_str = "None"
        if self.best_solution:
            # Sort for consistent output, especially for sets
            solution_str = str(sorted(list(self.best_solution)))
        
        content = [
            self.summary(),
            "",
            "Best solution (nodes set)",
            "---------------------------",
            solution_str,
        ]
        return "\n".join(content)

    # Retaining to_dict for potential backward compatibility or specific use cases,
    # though dataclasses provide __repr__ and asdict can be used.
    def to_dict(self) -> Dict[str, Any]:
        """
        将结果转换为字典形式
        
        返回:
            包含结果数据的字典
        """
        return {
            "best_solution": self.best_solution,
            "best_obj_value": self.best_obj_value,
            "num_iterations": self.num_iterations, # updated from iterations
            "runtime": self.runtime,
            # Note: stats object itself is not included, but could be serialized separately
            "has_statistics": self.stats is not None 
        } 