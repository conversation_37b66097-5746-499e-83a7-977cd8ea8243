#ifndef NWCNP_GRAPH_H
#define NWCNP_GRAPH_H

#include "../RandomNumberGenerator.h"
#include "Graph.h"
#include <cctype>
#include <fstream>
#include <iostream>
#include <memory>
#include <queue>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

/**
 * NWCNP_Graph
 *
 * Node-Weighted Critical Node Problem Graph.
 *
 * This class represents a graph for the Node-Weighted Critical Node Problem (NWCNP),
 * where each node has an associated weight. The objective is to find a set of nodes
 * to remove that maximizes the sum of weights of the remaining connected components
 * while satisfying certain constraints.
 *
 */
class NWCNP_Graph : public Graph
{
public:
    double bound;          ///< Knapsack capacity constraint. Represents the upper limit of total weight of nodes that can be removed.
    double SolWeight;      ///< Sum of weights of all removed nodes in the current solution.
    NodeSet removedNodes;  ///< Set storing nodes that have been removed (or selected for removal).

private:
    int numNodes;              ///< Total number of nodes in the graph (typically the original graph).
    NodeSet originalNodesSet;  ///< Set storing all original nodes in the graph, used for reference.
    std::vector<Age>
        nodeAge;  ///< Stores the "age" of each node, used in heuristic algorithms like tabu duration or selection preferences.
    std::unordered_map<Node, double>
        nodeWeights;  ///< Maps each node ID to its corresponding weight.
    std::vector<NodeSet> currentAdjList;  ///< Adjacency list representing the current state of the graph. `currentAdjList[i]`
                                          ///< contains all current neighbors of node `i`.
    std::vector<NodeSet>
        originalAdjList;  ///< Adjacency list representing the original structure of the graph, typically unchanged when the graph is modified, used as reference.

    std::vector<ComponentIndex>
        nodeToComponentIndex;  ///< Maps each node to the index of its current connected component.
    std::vector<Component>
        connectedComponents;  ///< List storing all connected components in the current graph.

    int connectedPairs;  ///< Number of connected pairs in the current graph state, typically used as the objective function value for NWCNP (to be maximized).

    RandomNumberGenerator rng;  ///< Random number generator instance, used for random decision processes within the class.

    /**
     * (Internal helper) Selects a larger connected component for subsequent operations (e.g., node removal).
     *
     * This is a heuristic strategy that may be used when choosing which connected component to remove nodes from,
     * prioritizing larger components as removing nodes from them may have a more significant impact on overall connectivity.
     * The specific definition of "larger" and selection logic is implemented within this function.
     *
     * Returns
     * -------
     * ComponentIndex
     *     Index of the selected larger connected component.
     */
    ComponentIndex selectRemovedLargerComponent() const;

    // Tarjan algorithm auxiliary member variables for finding cut vertices and bridges, or analyzing connectivity.
    // `mutable` allows them to be modified in const member functions (like impact calculation helper functions).
    mutable std::vector<int>
        dfn;  ///< Tarjan algorithm: Discovery Finishing Number of nodes.
    mutable std::vector<int>
        lowVec;  ///< Tarjan algorithm: Minimum dfn value a node can reach through at most one non-tree edge.
    mutable std::vector<int> stSizeVec;  ///< (Possibly for Tarjan auxiliary) Subtree size.
    mutable std::vector<int>
        cutSizeVec;  ///< (Possibly for Tarjan auxiliary) Size or impact of separation caused by node removal.
    mutable std::vector<double>
        impactVec;  ///< Stores some impact measure for each node, possibly calculated by Tarjan or other analysis.
    mutable std::vector<int>
        flagVec;  ///< General flag vector, can be used for state marking in DFS/BFS or Tarjan algorithms.
    mutable std::vector<bool> isCutVec;  ///< Marks whether a node is a cut vertex (critical node).
    mutable int timeStamp;  ///< Timestamp used in Tarjan algorithm for generating discovery times.
    mutable int
        nodeRoot;  ///< Root node of the current subtree or component being processed in Tarjan algorithm or related DFS.

    /**
     * (Internal helper) Executes Tarjan algorithm within a specified connected component.
     *
     * Tarjan algorithm is commonly used to find cut vertices, bridges, or strongly connected components in graphs.
     * This function executes within the connected component identified by `compIndex`, starting from `nodeIdx` (index in
     * the `nodeToIdx` mapping).
     *
     * Parameters
     * ----------
     * compIndex
     *     Index of the target connected component.
     * nodeIdx
     *     Index of the node to start Tarjan algorithm from in the `nodeToIdx` mapping.
     * nodeToIdx
     *     Vector mapping global node IDs to connected component internal node indices (or other local indices).
     */
    void tarjanInComponent(ComponentIndex compIndex,
                           int nodeIdx,
                           const std::vector<int> &nodeToIdx) const;

public:
    /**
     * Special constant value representing an invalid or non-existent node.
     */
    static constexpr Node INVALID_NODE
        = Graph::INVALID_NODE;  // Inherit from base

    NWCNP_Graph() = default;
    /**
     * Constructor for NWCNP_Graph.
     *
     * Initializes a new instance of the NWCNP_Graph class with the specified number of nodes.
     *
     * Parameters
     * ----------
     * numNodes
     *     The total number of nodes in the graph.
     */
    NWCNP_Graph(NodeSet nodes,
                         std::unordered_map<int, double> nodeWeights,
                         std::vector<NodeSet> adjList,
                         double bound,
                         int seed);

    /**
     * Creates a deep copy of this NWCNP_Graph instance.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     A unique pointer to a new NWCNP_Graph instance that is a deep copy of this instance.
     */
    std::unique_ptr<Graph> clone() const override;

    /**
     * Updates the graph by removing the specified set of nodes.
     *
     * Parameters
     * ----------
     * nodesToRemove
     *     The set of nodes to remove from the graph.
     */
    void updateGraphByRemovedNodes(const NodeSet &nodesToRemove) override;

    /**
     * Removes a single node from the graph.
     *
     * Parameters
     * ----------
     * nodeToRemove
     *     The ID of the node to remove.
     */
    void removeNode(Node nodeToRemove) override;

    /**
     * Adds a single node back to the graph.
     *
     * Parameters
     * ----------
     * nodeToAdd
     *     The ID of the node to add.
     */
    void addNode(Node nodeToAdd) override;

    /**
     * Sets the age of a specific node.
     *
     * Parameters
     * ----------
     * node
     *     The ID of the node.
     * age
     *     The age value to set.
     */
    void setNodeAge(Node node, Age age) override { nodeAge[node] = age; }

    /**
     * Gets the current objective function value.
     *
     * Returns
     * -------
     * int
     *     The current objective function value.
     */
    int getObjectiveValue() const override { return connectedPairs; }

    /**
     * Generates a random feasible graph instance.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     A unique pointer to a new random feasible NWCNP_Graph instance.
     */
    std::unique_ptr<Graph> getRandomFeasibleGraph() override;

    /**
     * Gets a reduced graph by removing the specified set of nodes.
     *
     * Parameters
     * ----------
     * nodesToRemove
     *     The set of nodes to remove.
     */
    void getReducedGraphByRemovedNodes(const NodeSet &nodesToRemove) override;

    /**
     * Checks if a specific node has been removed.
     *
     * Parameters
     * ----------
     * node
     *     The ID of the node to check.
     *
     * Returns
     * -------
     * bool
     *     True if the node has been removed, false otherwise.
     */
    bool isNodeRemoved(Node node) const override
    {
        return removedNodes.find(node) != removedNodes.end();
    }

    /**
     * Gets the set of all removed nodes.
     *
     * Returns
     * -------
     * const NodeSet&
     *     Constant reference to the set of all removed nodes.
     */
    const NodeSet& getRemovedNodes() const override { return removedNodes; }

    /**
     * Gets the total number of nodes in the graph.
     *
     * Returns
     * -------
     * int
     *     The total number of nodes.
     */
    int getNumNodes() const override { return numNodes; }

    /**
     * Selects a connected component to be removed.
     *
     * This method implements a heuristic strategy for selecting a connected component to remove nodes from,
     * prioritizing larger components as removing nodes from them may have a more significant impact on overall connectivity.
     * The specific definition of "larger" and selection logic is implemented within this function.
     *
     * Returns
     * -------
     * ComponentIndex
     *     The index of the selected connected component.
     */
    ComponentIndex selectRemovedComponent() const override;

    /**
     * Randomly selects a node from a specified connected component.
     *
     * Parameters
     * ----------
     * componentIndex
     *     The index of the connected component.
     *
     * Returns
     * -------
     * Node
     *     The ID of the randomly selected node.
     */
    Node randomSelectNodeFromComponent(ComponentIndex componentIndex) const override;

    /**
     * Selects a node from a specified connected component that has the minimum impact when removed.
     *
     * Parameters
     * ----------
     * componentIndex
     *     The index of the connected component.
     *
     * Returns
     * -------
     * Node
     *     The ID of the selected node.
     */
    Node impactSelectNodeFromComponent(ComponentIndex componentIndex) const override;

    /**
     * Selects a node from a specified connected component based on its age.
     *
     * Parameters
     * ----------
     * componentIndex
     *     The index of the connected component.
     *
     * Returns
     * -------
     * Node
     *     The ID of the selected node.
     */
    Node ageSelectNodeFromComponent(ComponentIndex componentIndex) const override;

    /**
     * Greedily selects a removed node to add back to the graph.
     *
     * Returns
     * -------
     * Node
     *     The ID of the selected node.
     */
    Node greedySelectNodeToAdd() const override;

    /**
     * Randomly selects a node from the graph for removal.
     *
     * Returns
     * -------
     * Node
     *     The ID of the randomly selected node.
     */
    Node randomSelectNodeToRemove() const override;

    /**
     * Calculates the connectivity gain if a specified node is added back to the graph.
     *
     * Parameters
     * ----------
     * node
     *     The ID of the node being considered.
     *
     * Returns
     * -------
     * int
     *     The connectivity gain.
     */
    int calculateConnectionGain(Node node) const override;

    /**
     * Initialize or recalculate the graph's connected components and node-to-component mapping.
     *
     * This method traverses the graph using DFS to identify all connected components,
     * populating the `connectedComponents` list and `nodeToComponentIndex` mapping.
     * It also calculates and updates `connectedPairs`.
     */
    void initializeComponentsAndMapping();

    /**
     * (Internal helper) Uses Depth-First Search (DFS) to find and return the connected
     * component containing the specified starting node.
     *
     * This is a helper function typically used internally by `initializeComponentsAndMapping`.
     *
     * Parameters
     * ----------
     * startNode
     *     Node ID to start DFS search from.
     *
     * Returns
     * -------
     * Component
     *     A `Component` object containing all nodes reachable from `startNode`.
     */
    Component DFSfindComponent(Node startNode) const;
};

#endif  // NWCNP_GRAPH_H
