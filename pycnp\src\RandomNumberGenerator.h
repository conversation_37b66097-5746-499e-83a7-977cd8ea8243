#ifndef RANDOM_NUMBER_GENERATOR_H
#define RANDOM_NUMBER_GENERATOR_H

#include <chrono>     // Used for generating a time-based default seed
#include <random>     // C++ standard library random number tools
#include <stdexcept>  // Used for throwing invalid_argument exceptions

/**
 * RandomNumberGenerator
 *
 * 随机数生成器类，封装了Mersenne Twister引擎
 *
 * 该类提供了生成概率值、整数、索引和布尔值的方法，
 * 为算法提供高质量的随机数支持。
 */
class RandomNumberGenerator
{
private:
    mutable std::mt19937
        rng;  ///< Mersenne Twister random number generation engine. `mutable`
              ///< allows it to be modified in const methods, as generating
              ///< random numbers inherently changes the engine's state.

public:
    /**
     * 默认构造函数
     *
     * 使用std::random_device获取非确定性种子来初始化随机数生成器
     */
    RandomNumberGenerator() : rng(std::random_device{}()) {}

    /**
     * 设置随机数种子
     *
     * Parameters
     * ----------
     * seed
     *     用于初始化随机数生成器的种子值
     */
    void setSeed(int seed) { rng.seed(static_cast<std::mt19937::result_type>(seed)); }

    /**
     *
     * Returns
     * -------
     * double
     *     The generated random probability value.
     */
    double generateProbability() const
    {
        std::uniform_real_distribution<double> dist(0.0, 1.0);
        return dist(rng);
    }

    /**
     *
     * Parameters
     * ----------
     * min
     *     The minimum value of the random integer (inclusive).
     * max
     *     The maximum value of the random integer (inclusive).
     *
     * Returns
     * -------
     * int
     *     The generated random integer.
     *
     * Throws
     * ------
     * std::invalid_argument
     *     If min > max.
     */
    int generateInt(int min, int max) const
    {
        if (min > max)
        {
            throw std::invalid_argument("Minimum cannot be greater than maximum");
        }
        std::uniform_int_distribution<int> dist(min, max);
        return dist(rng);
    }

    /**
     *
     * Parameters
     * ----------
     * max
     *     The upper bound for the index (exclusive). Must be positive.
     *
     * Returns
     * -------
     * int
     *     The generated random index.
     *
     * Throws
     * ------
     * std::invalid_argument
     *     If max <= 0.
     */
    int generateIndex(int max) const
    {
        if (max <= 0)
        {
            throw std::invalid_argument("max must be positive");
        }
        return generateInt(0, max - 1);
    }

    /**
     *
     * Parameters
     * ----------
     * p
     *     The probability of generating true.
     *
     * Returns
     * -------
     * bool
     *     True if the generated random probability is less than p, otherwise false.
     */
    bool generateBool(double p) const { return generateProbability() < p; }
};

#endif  // RANDOM_NUMBER_GENERATOR_H
