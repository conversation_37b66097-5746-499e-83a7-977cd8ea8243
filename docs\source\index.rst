PyCNP
==================

PyCNP is an open-source, state-of-the-art Critical Node Problem (CNP) solver.
It currently supports CNPs with:

- Critical node problem ;
- Node-weighted critical node problem;
- Distance-based critical node problem;

The PyCNP package comes with pre-compiled binaries for Windows, Mac OS and Linux, and can thus be easily installed without requiring local compilation.
It can be installed through *pip* via

.. code-block:: shell

   pip install pycnp

Contents
--------

.. toctree::
   :maxdepth: 1
   :caption: Getting Started

   setup/introduction_to_cnp
   setup/introduction_to_ma
   setup/installation
   setup/benchmarks
   setup/citing

.. toctree::
   :maxdepth: 1
   :caption: Examples

   examples/index

.. toctree::
   :maxdepth: 1
   :caption: API reference

   api/pycnp
   api/population
   api/crossover
   api/search
   api/graph
   api/stop

