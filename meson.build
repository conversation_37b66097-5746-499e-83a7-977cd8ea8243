project('pycnp', 'cpp',
  version : '0.1.1.dev0',
  license : 'MIT',
  default_options : [
    'cpp_std=c++20',        # 使用C++20以支持现代特性
    'warning_level=3',      # 启用高级别警告
    'optimization=3',       # 优化级别3
    'debug=false',          # 发布版本不包含调试信息
    'strip=true',           # 去除符号表以减小文件大小
    'b_ndebug=true'         # 定义NDEBUG宏，禁用assert
  ]
)

# 查找Python和pybind11
py = import('python').find_installation()
pybind11_dep = dependency('pybind11', fallback : ['pybind11', 'pybind11_dep'])

# 定义源文件目录
pycnp_src_dir = 'pycnp/src'
generated_docs_dir = 'generated_docs'

# 收集所有C++源文件
cpp_sources = files([
  pycnp_src_dir / 'Crossover.cpp',
  pycnp_src_dir / 'Population.cpp',
  pycnp_src_dir / 'ProblemData.cpp',
  pycnp_src_dir / 'Graph/CNP_Graph.cpp',
  pycnp_src_dir / 'Graph/DCNP_Graph.cpp',
  pycnp_src_dir / 'Graph/NWCNP_Graph.cpp',
  pycnp_src_dir / 'search/BCLSStrategy.cpp',
  pycnp_src_dir / 'search/CBNSStrategy.cpp',
  pycnp_src_dir / 'search/CHNSStrategy.cpp',
  pycnp_src_dir / 'search/DLASStrategy.cpp',
  pycnp_src_dir / 'search/Search.cpp'
])

# 收集所有头文件（用于依赖跟踪）
cpp_headers = files([
  pycnp_src_dir / 'Crossover.h',
  pycnp_src_dir / 'Population.h',
  pycnp_src_dir / 'ProblemData.h',
  pycnp_src_dir / 'RandomNumberGenerator.h',
  pycnp_src_dir / 'SearchResult.h',
  pycnp_src_dir / 'Graph/Graph.h',
  pycnp_src_dir / 'Graph/CNP_Graph.h',
  pycnp_src_dir / 'Graph/DCNP_Graph.h',
  pycnp_src_dir / 'Graph/NWCNP_Graph.h',
  pycnp_src_dir / 'search/SearchStrategy.h',
  pycnp_src_dir / 'search/BCLSStrategy.h',
  pycnp_src_dir / 'search/CBNSStrategy.h',
  pycnp_src_dir / 'search/CHNSStrategy.h',
  pycnp_src_dir / 'search/DLASStrategy.h',
  pycnp_src_dir / 'search/Search.h'
])

# 定义生成的文档头文件
docstrings_header = 'pycnp_autogen_docs.h'

# 收集所有头文件用于文档提取
find_prog = find_program('find')
header_files_result = run_command(find_prog, meson.current_source_dir() / pycnp_src_dir, '-name', '*.h', '-type', 'f', check: true)
header_files = header_files_result.stdout().strip().split('\n')

# 生成文档字符串头文件的自定义目标
docstrings_target = custom_target('generate_docstrings',
  output : 'pycnp_autogen_docs.h',
  input : files('scripts/extract_docstrings.py'),
  command : [
    py.full_path(),
    '@INPUT@',
    '@OUTPUT@'
  ] + header_files,
  build_by_default : true
)

# 包含目录
inc_dirs = include_directories(
  pycnp_src_dir,
  '.'  # 包含生成的文档头文件目录
)

# 编译选项
cpp_args = []
if get_option('buildtype') == 'debug'
  cpp_args += ['-DDEBUG']
endif

# 根据编译器设置警告选项
if meson.get_compiler('cpp').get_id() == 'msvc'
  cpp_args += ['/W4']
else
  cpp_args += ['-Wall', '-Wextra']
endif

# 创建Python扩展模块
py.extension_module('_pycnp',
  sources : [
    pycnp_src_dir / 'pybind.cpp',
    cpp_sources,
    docstrings_target
  ],
  include_directories : inc_dirs,
  dependencies : [pybind11_dep],
  cpp_args : cpp_args,
  install : true,
  subdir : 'pycnp'
)

# 安装Python包文件
py.install_sources([
  'pycnp/__init__.py',
  'pycnp/MemeticSearch.py',
  'pycnp/Model.py',
  'pycnp/ProgressPrinter.py',
  'pycnp/Result.py',
  'pycnp/Statistics.py',
  'pycnp/_pycnp.pyi',
  'pycnp/read.py',
  'pycnp/graph_visualization.py'
], subdir: 'pycnp')

# 安装stop子包
py.install_sources([
  'pycnp/stop/__init__.py'
], subdir: 'pycnp/stop')
