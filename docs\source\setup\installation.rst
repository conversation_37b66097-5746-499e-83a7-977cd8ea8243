Installation instructions
=========================

The most straightforward way to use the ``pycnp`` package in your project is to install via *pip*, like so:

.. code-block:: shell

   pip install pycnp

.. note::

   PyCNP comes with pre-compiled binaries for Windows, macOS, and Linux, so no local compilation is required for most users.


Installing from source
----------------------

To install the latest development version of ``pycnp`` directly from the GitHub repository, you can use *pip*, like so:

.. code-block:: shell

   pip install 'pycnp @ git+https://github.com/xuebo100/PyCNP'

This can be useful to get updates that have not yet made it to the Python package index.

Alternatively, you can clone the repository and build from source:

.. code-block:: shell

   git clone https://github.com/xuebo100/PyCNP.git
   cd PyCNP
   pip install .

.. warning::

   Building from source requires a C++17 compatible compiler and CMake. On Windows, you may need Visual Studio 2019 or later.


.. _running-locally:

Running the examples locally
----------------------------

To run the example scripts and notebooks locally, first clone the repository:

.. code-block:: shell

   git clone https://github.com/xuebo100/PyCNP.git
   cd PyCNP

**Option 1: Using pip (Recommended)**

Install PyCNP in development mode with example dependencies:

.. code-block:: shell

   pip install -e ".[examples]"

**Option 2: Using Poetry**

If you prefer using Poetry for dependency management:

.. code-block:: shell

   pip install --upgrade poetry
   poetry install --with examples

**Running the examples**

After installation, you can run the Python examples directly:

.. code-block:: shell

   # Run a basic CNP example
   python examples/basic_cnp_example.py

   # Run a NWCNP example
   python examples/nwcnp_example.py

If you installed with Poetry, prefix commands with ``poetry run``:

.. code-block:: shell

   poetry run python examples/basic_cnp_example.py

**Jupyter Notebooks**

To run Jupyter notebooks (if available):

.. code-block:: shell

   # With pip installation
   jupyter notebook

   # With Poetry
   poetry run jupyter notebook

This will open up a page in your browser, where you can navigate to the example notebooks in the ``examples/`` folder!

System Requirements
-------------------

PyCNP requires:

- Python 3.8 or later
- NumPy (automatically installed)
- For development: CMake 3.15+, C++17 compatible compiler

**Supported Platforms:**

- Windows 10/11 (x64)
- macOS 10.15+ (Intel and Apple Silicon)
- Linux (x64) with glibc 2.17+
