from typing import (
    Callable, ClassVar, Dict, Final, List, Optional, Set, Tuple, overload
)

# Constants
CBNS: Final[str]
CHNS: Final[str]
DLAS: Final[str]
BCLS: Final[str]
CNP: Final[str]
DCNP: Final[str]
NWCNP: Final[str]
DBX: Final[str]
RSC: Final[str]
IRR: Final[str]

# --- Classes ---

class MSParams:
    """参数配置类，用于模因搜索算法"""
    search: str
    """搜索策略"""
    crossover: str
    """交叉算子"""
    is_pop_variable: bool
    """是否可变种群"""
    initial_pop_size: int
    """初始种群大小"""
    @overload
    def __init__(self) -> None: ...
    # @overload # Assuming these are for Python-side convenience if needed, not direct C++ struct members
    # def __init__(self, search_strategy: str) -> None: ...
    def __repr__(self) -> str: ...

    
class VPParams:
    """参数配置类，用于模因搜索算法"""
    maxPopSize: int
    """最大种群大小"""
    increasePopSize: int
    """种群大小增加量"""
    maxIdleGens: int
    """最大空闲代数，用于停止准则"""
    @overload
    def __init__(self) -> None: ...
    # @overload # Assuming these are for Python-side convenience if needed, not direct C++ struct members
    # def __init__(self, search_strategy: str) -> None: ...
    def __repr__(self) -> str: ...

class SearchResult:
    """搜索结果类"""
    obj_value: int  # Changed from float
    """目标函数值"""
    @property
    def solution(self) -> 'Solution':
        """解，表示移除的节点集合"""
        ...
    def __init__(self) -> None: ...

class Graph:
    """图基类，封装了图的基本操作和属性。

    该类通常不直接实例化，而是作为 CNP_Graph, DCNP_Graph, NWCNP_Graph 等具体问题图类的基类。
    它提供了节点/边的管理、连通性检查、组件分析等通用图功能。
    图的修改（如添加/移除节点）会影响其状态和通过方法（如 get_objective_value）计算的值。

    Attributes:
        problem_type_name (str): 图的问题类型名称 (例如 "CNP", "DCNP", "NWCNP")。
        INVALID_NODE (ClassVar[int]): 表示无效节点的常量值。
    """
    INVALID_NODE: ClassVar[int] = -1
    # 构造函数通常在派生类中定义并调用基类逻辑
    # def __init__(self, problem_data: 'ProblemData', initial_removed_nodes: Optional[Solution] = None) -> None: ...

    @property
    def problem_type_name(self) -> str:
        """获取图的问题类型名称 (例如 "CNP", "DCNP", "NWCNP")。"""
        ...

    def clone(self) -> 'Graph':
        """创建当前图对象的一个深拷贝。

        返回的图对象与原始图具有相同的具体类型 (例如，CNP_Graph 的克隆仍然是 CNP_Graph)。

        Returns:
            Graph: 一个新的图实例，是当前图的深拷贝。
        """
        ...

    def get_objective_value(self) -> int:
        """获取当前图的目标函数值。

        目标值的具体计算方式取决于派生图的类型 (CNP, DCNP, NWCNP) 及其具体实现。
        例如，对于CNP，可能是连通对数；对于NWCNP，根据绑定可能是一个值，而实际权重和通过其他方法获取。

        Returns:
            int: 当前图的目标函数值。
        """
        ...

    def get_removed_nodes(self) -> 'Solution':
        """获取当前图中已被移除的节点集合。

        Returns:
            Solution: 一个包含所有已移除节点ID的集合。
        """
        ...

    def get_random_feasible_graph(self, seed: int) -> 'Graph':
        """生成并返回一个当前图的随机可行副本。

        "可行"意味着生成的图满足特定问题类型的约束（例如，CNP的连通性，DCNP的距离限制等）。
        这个方法通常用于种群初始化或多样性引入。

        Args:
            seed (int): 用于随机数生成的种子。

        Returns:
            Graph:一个新的图实例，表示一个随机的可行解。返回类型为当前图的实际类型。
        """
        ...

    def is_node_removed(self, node_id: int) -> bool:
        """检查指定的节点当前是否已被移除。

        Args:
            node_id (int): 要检查的节点的ID。

        Returns:
            bool: 如果节点已被移除，则为 True；否则为 False。
        """
        ...

    def get_num_nodes(self) -> int:
        """获取图的原始节点总数（包括已移除和未移除的）。

        Returns:
            int: 图中的节点总数。
        """
        ...

    def add_node(self, node_id: int) -> None:
        """将一个之前被移除的节点添加回图中。

        如果节点已存在于图中（未被移除），此操作可能没有效果或导致错误，具体取决于实现。
        这会改变图的状态，可能影响目标值和连通性。

        Args:
            node_id (int): 要添加回图中的节点的ID。
        """
        ...

    def remove_node(self, node_id: int) -> None:
        """从图中移除一个节点。

        如果节点已被移除或不存在于原始图中，此操作可能没有效果。
        这会改变图的状态，可能影响目标值和连通性。

        Args:
            node_id (int): 要从图中移除的节点的ID。
        """
        ...

    def set_node_age(self, node_id: int, age: int) -> None:
        """设置图中某个节点的年龄。

        节点的年龄可以用于某些启发式算法中，例如在选择节点进行操作时。

        Args:
            node_id (int): 要设置年龄的节点ID。
            age (int): 要设置的年龄值。
        """
        ...

    def reduce_graph_by_removed_nodes(self, nodes_to_remove: Solution) -> None:
        """根据提供的节点集合，在原地简化当前图的结构。

        此操作会从图中移除指定的节点以及与这些节点相关联的所有边。
        主要用于图的预处理或在算法的某些阶段永久性地移除部分节点。

        Args:
            nodes_to_remove (Solution): 一个包含要从图中移除的节点ID的集合。
        """
        ...

    def get_num_connected_components(self) -> int:
        """计算并返回当前图中连通分量的数量。

        一个连通分量是一组节点，其中任意两个节点之间都存在路径。
        如果图是空的或所有节点都被移除，则返回0或节点数，具体取决于实现。

        Returns:
            int: 图中连通分量的数量。
        """
        ...


    def get_largest_component_size(self) -> int:
        """获取当前图中最大连通分量的大小（节点数）。

        Returns:
            int: 最大连通分量中的节点数量。如果图为空，返回0。
        """
        ...

    def get_num_edges(self) -> int:
        """获取图中当前存在的边数。

        边的数量会随着节点的移除或添加而改变，因为与移除节点相关的边也会被移除。

        Returns:
            int: 图中当前（未移除部分）的边数。
        """
        ...

    def set_removed_nodes(self, nodes: 'Solution') -> None:
        """直接设置图中移除的节点集合。

        这会完全替换当前的移除节点集合。图的状态将根据新的移除节点集合更新。

        Args:
            nodes (Solution): 一个包含要设置为已移除状态的节点ID的集合。
        """
        ...


    def select_removed_component(self) -> int: # Assuming ComponentIndex is int
        """选择一个连通分量，通常是从中选择节点进行移除。

        选择策略取决于具体的图类型和算法实现。

        Returns:
            int: 被选中的连通分量的索引。
        """
        ...

    def random_select_node_from_component(self, component_index: int) -> int:
        """从指定的连通分量中随机选择一个节点。

        Args:
            component_index (int): 目标连通分量的索引。

        Returns:
            int: 被选中的节点ID。如果分量为空或无效，可能返回 INVALID_NODE。
        """
        ...

    def impact_select_node_from_component(self, component_index: int) -> int:
        """从指定的连通分量中选择具有最大影响力的节点。

        "影响力"的具体定义取决于问题类型（例如，移除后能最大程度破坏连通性的节点）。

        Args:
            component_index (int): 目标连通分量的索引。

        Returns:
            int: 被选中的节点ID。
        """
        ...

    def age_select_node_from_component(self, component_index: int) -> int:
        """基于节点的"年龄"从指定的连通分量中选择一个节点。

        年龄可以指示节点在解中的停留时间或其他启发式信息。

        Args:
            component_index (int): 目标连通分量的索引。

        Returns:
            int: 被选中的节点ID。
        """
        ...

    def greedy_select_node_to_add(self) -> int:
        """贪婪地选择一个当前已移除的节点添加回图中，以期最大程度改善解的质量。

        选择标准取决于具体问题和图类型。

        Returns:
            int: 被选中的、要添加回图中的节点ID。如果无合适节点，可能返回 INVALID_NODE。
        """
        ...

    def random_select_node_to_remove(self) -> int:
        """从当前图中（未被移除的节点中）随机选择一个节点以进行移除。

        Returns:
            int: 被选中的、要移除的节点ID。如果图中无可选节点，可能返回 INVALID_NODE。
        """
        ...

    def calculate_connection_gain(self, node_id: int) -> int:
        """计算如果将指定的（当前已移除的）节点添加回图中，会带来的连接性增益。

        增益的具体计算方式取决于问题定义（例如，新增的连通对数量）。

        Args:
            node_id (int): 考虑要添加回图中的节点ID。

        Returns:
            int: 连接增益值。
        """
        ...

class CNP_Graph(Graph):
    """连通性节点问题 (CNP) 的图表示。

    CNP的目标通常是移除最少数量的节点，同时保持图的其余部分连通。
    此类继承自 Graph，并实现了 CNP 特定的逻辑，例如可行性检查。
    """
    def __init__(self, problem_data: 'ProblemData', initial_removed_nodes: Optional['Solution'] = None) -> None:
        """构造一个 CNP_Graph 实例。

        Args:
            problem_data (ProblemData): 包含图的拓扑结构（节点、边）的原始问题数据。
            initial_removed_nodes (Optional[Solution]): 初始时要从图中移除的节点集合。默认为空集合。
        """
        ...

class DCNP_Graph(Graph):
    """距离受限连通性节点问题 (DCNP) 的图表示。

    DCNP 不仅要求图保持连通，还要求图中任意两个未移除节点之间的路径长度不能超过预设的距离限制。
    此类继承自 Graph，并实现了 DCNP 特定的逻辑。
    """
    def __init__(self, problem_data: 'ProblemData', initial_removed_nodes: Optional['Solution'] = None) -> None:
        """构造一个 DCNP_Graph 实例。

        Args:
            problem_data (ProblemData): 包含图的拓扑结构和距离限制的原始问题数据。
            initial_removed_nodes (Optional[Solution]): 初始时要从图中移除的节点集合。默认为空集合。
        """
        ...

    def calculate_khop_tree_size(self, k: int) -> int:
        """计算图中所有节点的 k-hop 邻域大小之和。

        一个节点的 k-hop 邻域包含所有在 k 跳（或更少）内可达的节点。
        此方法用于评估图的局部连通性或密度，通常是 DCNP 的目标函数。
        注意：参数 k 的行为可能取决于底层的 C++ 实现；如果 DCNP_Graph 实例的 k-hop 限制是固定的，
        此参数可能被忽略或用于特定的临时查询。

        Args:
            k (int): 跳数限制。

        Returns:
            int: 所有节点的 k-hop 邻域大小的总和。
        """
        ...

    def calculate_betweenness_centrality(self) -> Dict[int, float]:
        """计算图中所有节点的介数中心性。

        介数中心性衡量节点在网络中作为"桥梁"的重要性，即网络中节点对之间的最短路径经过该节点的频率。

        Returns:
            Dict[int, float]: 一个字典，键是节点ID，值是该节点的介数中心性得分。
        """
        ...   

    def build_tree(self) -> None:
        """为 DCNP 图构建（或更新）内部的 K-跳树相关数据结构。

        此方法通常在图的节点状态（添加/移除）发生变化后，或者在需要计算
        K-跳相关的目标函数（如 `calculate_khop_tree_size`）之前调用，以确保
        相关数据是最新的。
        """
        ...

    def find_best_node_to_remove(self) -> int:
        """为 DCNP 图找到当前移除后能最大程度改善目标函数（通常是最大化 K-跳树总大小）的节点。

        这是一个启发式方法，用于指导搜索过程。

        Returns:
            int: 推荐移除的节点ID。如果没有合适的节点或图为空，可能返回 INVALID_NODE。
        """
        ...

    def find_best_node_to_add(self) -> int:
        """为 DCNP 图找到从已移除节点中选择一个添加回图中，能最大程度改善目标函数的节点。

        这是一个启发式方法，用于指导搜索过程。

        Returns:
            int: 推荐添加回图中的已移除节点ID。如果没有合适的节点，可能返回 INVALID_NODE。
        """
        ...

class NWCNP_Graph(Graph):
    """带节点权重的连通性节点问题 (NWCNP) 的图表示。

    NWCNP 的目标通常是移除总权重尽可能小的节点集，同时保持图的其余部分连通。
    每个节点关联一个权重。
    """
    def __init__(self, problem_data: 'ProblemData', initial_removed_nodes: Optional['Solution'] = None) -> None:
        """构造一个 NWCNP_Graph 实例。

        Args:
            problem_data (ProblemData): 包含图的拓扑结构和节点权重的原始问题数据。
            initial_removed_nodes (Optional[Solution]): 初始时要从图中移除的节点集合。默认为空集合。
        """
        ...

    def get_objective_value(self) -> int: # Overrides Graph.get_objective_value
        """获取当前图的目标函数值。对于 NWCNP 的此特定绑定，该方法可能返回与连通性相关的度量（例如连通对数），
        而不是移除节点的权重和。

        要获取被移除节点的总权重（通常是 NWCNP 的主要优化目标），请使用 `get_sum_of_weights_of_removed_nodes()` 方法。

        Returns:
            int: 一个与图当前状态相关的整数值（例如，连通对数）。
        """
        ...


    def get_node_weight(self, node_id: int) -> float:
        """获取指定节点的权重。

        Args:
            node_id (int): 要查询其权重的节点ID。

        Returns:
            float: 指定节点的权重。如果节点不存在或没有定义权重，可能返回0或抛出错误。
        """
        ...

    def get_total_node_weight(self) -> float:
        """获取图中所有原始节点（无论是否被移除）的总权重。

        Returns:
            float: 图中所有节点的权重之和。
        """
        ...

    def get_sum_of_weights_of_removed_nodes(self) -> float:
        """计算并返回当前所有已被移除节点的权重之和。

        这通常是 NWCNP 问题中要最小化的目标函数值。

        Returns:
            float: 所有已移除节点的权重总和。
        """
        ...


class Crossover:
    """交叉算子类"""
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, seed: int) -> None: ...
    def set_seed(self, seed: int) -> None:
        """设置随机数种子"""
        ...
    def double_backbone_based_crossover(self, orig_graph: Graph, parent1: 'Solution', parent2: 'Solution') -> Graph: 
        """基于双骨干的交叉算子 (需要2个父代)"""
        ...
    def reduce_solve_combine(self, orig_graph: Graph, parent1: 'Solution', parent2: 'Solution') -> Graph: 
        """规约-求解-合并 交叉算子"""
        ...
    def inherit_repair_recombination(self, orig_graph: Graph, parent1: 'Solution', parent2: 'Solution', parent3: 'Solution') -> Graph:
        """继承修复重组交叉算子 (Inherit_repair_recombination_crossover) (需要3个父代)"""
        ...

class Search:
    """局部搜索算法类，用于在给定图上执行各种搜索策略以找到优化解。

    该类采用策略模式，允许通过名称动态设置和切换不同的搜索算法。
    在运行搜索之前，必须先通过 `set_strategy` 方法指定一个搜索策略。
    可以通过 `get_available_strategies` 获取所有已注册的策略名称。
    """
    @overload
    def __init__(self, graph: Graph, seed: int) -> None:
        """构造一个新的 Search 实例，并指定随机数种子。

        Args:
            graph (Graph): 将在其上执行搜索的图对象。该图对象可能会在搜索过程中被修改。
            seed (int): 用于初始化搜索算法内部随机数生成器的种子。
        """
        ...

    def set_strategy(self, strategy: str) -> None:
        """设置要使用的局部搜索策略。

        Args:
            strategy (str): 要使用的策略的名称。必须是 `get_available_strategies()` 返回的有效策略之一。
                可用的策略包括：
                - "CBNS" (Component-Based Neighborhood Search): 基于组件的邻域搜索策略，通过操作图的连通组件来搜索最优解。
                - "CHNS" (Component-Based Hybrid Neighborhood Search): 基于分量的混合邻域搜索策略，结合了两种启发式方法进行搜索。
                - "DLAS" (Diversified Late Acceptance Search): 多样性的延迟接受搜索策略，使用历史成本接受准则来搜索最优解。
                - "BCLS" (Betweenness Centrality-based Late-acceptance Search): 基于介数中心性的延迟接受搜索策略，使用节点的介数中心性指导搜索。
        
        Raises:
            ValueError: 如果提供的策略名称无效或不受支持。
        """
        ...

    def run(self) -> SearchResult:
        """运行先前通过 `set_strategy` 设置的局部搜索算法。

        Returns:
            SearchResult: 包含找到的最佳解及其目标值的搜索结果对象。

        Raises:
            RuntimeError: 如果在调用此方法之前没有设置搜索策略。
        """
        ...

    def get_available_strategies(self) -> List[str]:
        """获取所有已注册且可用的局部搜索策略的名称列表。

        Returns:
            List[str]: 一个包含可用策略名称的字符串列表 (例如, ["CBNS", "CHNS", "DLAS", "BCLS"])。
        """
        ...


class ProblemData:
    """问题数据类，用于读取和存储图信息"""
    def __init__(self, num_nodes: int, instance_name: Optional[str] = None) -> None: ...

    @property
    def instance_name(self) -> str:
        """问题实例的名称"""
        ...
    @classmethod
    def read_from_adjacency_list_file(cls, filename: str) -> 'ProblemData':
        """从邻接表文件读取图数据"""
        ...
    @classmethod
    def read_from_edge_list_format(cls, filename: str) -> 'ProblemData':
        """从边列表文件读取图数据"""
        ...
    def read_node_weights_from_file(self, filename: str) -> None:
        """从文件读取节点权重 (用于 NWCNP)"""
        ...
    def create_original_graph(self, problem_type: str) -> Graph:
        """根据问题类型创建初始图对象 (例如 "CNP", "DCNP", "NWCNP")"""
        ...
    def add_node(self, node_id: int) -> None: # Parameter name 'u' in C++
        """添加一个节点 (主要用于构建图)"""
        ...
    def add_edge(self, u: int, v: int, weight: float = 1.0) -> None: # Added weight
        """添加一条边"""
        ...
    def add_node_weight(self, node_id: int, weight: float) -> None: # Parameter name 'u' in C++
        """添加节点权重"""
        ...
    def get_node_weight(self, node_id: int) -> float: # Parameter name 'u' in C++
        """获取节点权重"""
        ...
    def num_nodes(self) -> int: # C++ is get_num_nodes
        """获取节点数量"""
        ...
    def get_num_edges(self) -> int:
        """获取边数量"""
        ...
    

class Population:
    """种群类，用于管理模因算法中的解"""
    def __init__(self, graph: Graph, params: MemeticSearchParams, seed: int) -> None: ...
    def update(self, solution: 'Solution', obj_value: int, verbose: bool) -> None: # Updated signature
        """更新种群 (例如，加入新解，移除最差解，处理空闲迭代)"""
        ...
    def initialize(self, verbose: bool, stopping_criterion: Optional[Callable[[int], bool]]) -> Tuple['Solution', int]: # obj_value in callback and return to int
        """
        初始化种群

        Args:
            verbose: 是否打印详细信息
            stopping_criterion: 可选的停止准则回调函数 (输入最佳目标值 int，返回是否停止 bool)

        Returns:
            一个包含最佳解 (Solution) 和其目标值 (int) 的元组
        """
        ...
    def select(self, k: int = 2) -> Tuple['Solution', 'Solution']: # Renamed and added k
        """通过k元锦标赛选择两个不同的解作为父代"""
        ...
    def get_all_three_solutions(self) -> Tuple['Solution', 'Solution', 'Solution']: # Changed return type
        """获取种群中所有三个不同的解"""
        ...
    def expand(self) -> None:
        """扩展种群 (如果 IS_POP_VARIABLE 为 True)"""
        ...
    def rebuild(self) -> None:
        """重建种群"""
        ...
    def get_size(self) -> int:
        """获取当前种群大小"""
        ...
    

# 类型别名
Solution = Set[int] 