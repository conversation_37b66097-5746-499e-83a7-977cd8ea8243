#include "Population.h"
#include "Crossover.h"
#include <algorithm>
#include <iostream>
#include <limits>
#include <memory>
#include <numeric>
#include <stdexcept>

void Population::update(const Solution &newSolution,
                        int objValue,
                        int num_idle_generations,
                        bool verbose)
{
    Item newItem(newSolution, objValue, 0.0);

    for (auto &item : population)
    {
        double similarity = computeSimilarity(item.solution, newSolution);
        newItem.similarity.push_back({similarity, item.solution});
        item.similarity.push_back({similarity, newSolution});
    }

    population.push_back(newItem);

    removeWorstSolution();

    if (isVariablePopulation
        && num_idle_generations > 0
        && num_idle_generations % variablePopulationParams.maxIdleGens == 0
        && static_cast<int>(population.size()) < variablePopulationParams.maxPopSize)
    {
        if (verbose)
        {
            std::cout << "----------------Expanding population due to idle "
                         "steps----------------"
                      << std::endl;
        }
        expand();
    }
    else if (isVariablePopulation
             && num_idle_generations > 0
             && num_idle_generations % variablePopulationParams.maxIdleGens == 0
             && static_cast<int>(population.size()) >= variablePopulationParams.maxPopSize)
    {
        if (verbose)
        {
            std::cout << "----------------Rebuilding population due to size "
                         "limit----------------"
                      << std::endl;
        }
        rebuild();
    }
}

void Population::add(const Solution &newSolution, int objValue)
{

    Solution solution = newSolution;

    Item item(solution, objValue, 0.0);

    for (auto &otherItem : population)
    {
        double similarity
            = computeSimilarity(item.solution, otherItem.solution);
        item.similarity.push_back({similarity, otherItem.solution});
        otherItem.similarity.push_back({similarity, item.solution});
    }

    population.push_back(item);
}

void Population::removeWorstSolution()
{

    updateFitness();
    auto worstIt = std::max_element(population.begin(),
                                    population.end(),
                                    [](const Item &a, const Item &b)
                                    { return a.fitness < b.fitness; });

    for (auto &item : population)
    {
        if (item.solution != worstIt->solution)
        {

            auto &similarity = item.similarity;
            similarity.erase(
                std::remove_if(
                    similarity.begin(),
                    similarity.end(),
                    [worstSolution = worstIt->solution](const auto &pair)
                    { return pair.second == worstSolution; }),
                similarity.end());
        }
    }

    population.erase(worstIt);
}

void Population::updateFitness()
{
    const size_t popSize = population.size();
    if (popSize <= 1)
    {

        if (popSize == 1)
        {
            population[0].fitness = 0.0;
        }
        return;
    }

    std::vector<double> costs(popSize);
    std::vector<double> diversityScores(popSize);

    for (size_t i = 0; i < popSize; i++)
    {

        costs[i] = population[i].objValue;

        const auto &similarities = population[i].similarity;

        if (!similarities.empty())
        {

            double sumSimilarity = 0.0;
            for (const auto &[similarity, _] : similarities)
            {
                sumSimilarity += similarity;
            }
            diversityScores[i] = sumSimilarity / similarities.size();
        }
        else
        {

            diversityScores[i] = 0.0;
        }
    }

    auto calculateRanks
        = [](const std::vector<double> &values) -> std::vector<int>
    {
        const size_t n = values.size();

        std::vector<size_t> indices(n);
        std::iota(indices.begin(), indices.end(), 0);

        std::stable_sort(indices.begin(),
                         indices.end(),
                         [&values](size_t i1, size_t i2)
                         {
                             if (values[i1] != values[i2])
                             {
                                 return values[i1] < values[i2];
                             }

                             return i1 < i2;
                         });

        std::vector<int> ranks(n);
        for (size_t i = 0; i < n; i++)
        {
            ranks[indices[i]] = static_cast<int>(i + 1);
        }
        return ranks;
    };

    std::vector<int> costRanks = calculateRanks(costs);
    std::vector<int> diversityRanks = calculateRanks(diversityScores);

    for (size_t i = 0; i < popSize; i++)
    {
        population[i].fitness
            = ALPHA * costRanks[i] + (1.0 - ALPHA) * diversityRanks[i];
    }
}

bool Population::isDuplicate(const Solution &solution) const
{

    for (const auto &item : population)
    {
        if (item.solution == solution)
        {
            return true;
        }
    }
    return false;
}

std::tuple<Solution, Solution, Solution>
Population::getAllThreeSolutions() const
{
    if (population.size() != 3)
    {
        throw std::runtime_error("Population size must be 3 to return all three solutions");
    }

    return std::make_tuple(
        population[0].solution, population[1].solution, population[2].solution);
}

const Population::Item &Population::getBestItem() const
{

    return *std::min_element(population.begin(),
                             population.end(),
                             [](const Item &a, const Item &b)
                             { return a.objValue < b.objValue; });
}

std::pair<Solution, int> Population::generateNonDuplicateSolution()
{

    static int seedCounter = 1000;
    int deterministicSeed = seedCounter++;

    std::unique_ptr<Graph> newGraph = originalGraph.getRandomFeasibleGraph();

    Search searchObj(*newGraph, deterministicSeed);

    searchObj.setStrategy(search);
    
    SearchResult result = searchObj.run();

    int attempts = 0;
    const int MAX_ATTEMPTS = 10;

    while (isDuplicate(newGraph->getRemovedNodes()) && attempts < MAX_ATTEMPTS)
    {

        Node addedNode;
        if (dynamic_cast<const DCNP_Graph *>(&originalGraph))
        {

            addedNode = newGraph->findBestNodeToAdd();
        }
        else
        {

            addedNode = newGraph->greedySelectNodeToAdd();
        }
        newGraph->addNode(addedNode);

        auto removedNode = newGraph->randomSelectNodeToRemove();
        newGraph->removeNode(removedNode);

        attempts++;
    }

    return std::make_pair(newGraph->getRemovedNodes(),
                          newGraph->getObjectiveValue());
}

void Population::expand()
{

    const size_t newSize
        = population.size() +  variablePopulationParams.increasePopSize;
    population.reserve(newSize);

    while (population.size() < newSize)
    {

        auto [solution, objValue] = generateNonDuplicateSolution();

        add(solution, objValue);
    }
}

void Population::rebuild()
{

    Item bestItem = getBestItem();

    population.clear();

    population.reserve(2);

    population.push_back(bestItem);

    population[0].similarity.clear();

    auto [solution, objValue] = generateNonDuplicateSolution();

    add(solution, objValue);
}

std::pair<Solution, int>
Population::initialize(bool display,
                       std::function<bool(int)> stopping_criterion)
{
    population.clear();
    
    for (int i = 0; i < initPopSize; ++i)
    {
        auto [newSolution, objValue] = generateNonDuplicateSolution();

        if (stopping_criterion and stopping_criterion(objValue))
        {
            if (display)
            {
                std::cout << "Stopping criterion met during initialization."
                          << std::endl;
            }
            return {newSolution, objValue};
        }

        add(newSolution, objValue);
    }

    const auto &bestItem = getBestItem();
    return {bestItem.solution, bestItem.objValue};
}

std::pair<Solution, Solution> Population::tournamentSelectTwoSolutions(int k)
{

    k = std::min(k, static_cast<int>(population.size()));

    updateFitness();

    size_t parent1Index = 0;
    {

        std::vector<size_t> candidates;
        candidates.reserve(k);

        for (int i = 0; i < k; ++i)
        {
            size_t candidateIndex = rng->generateIndex(population.size());
            candidates.push_back(candidateIndex);
        }

        parent1Index = *std::min_element(
            candidates.begin(),
            candidates.end(),
            [this](size_t i, size_t j)
            { return population[i].fitness < population[j].fitness; });
    }

    size_t parent2Index;
    {
        std::vector<size_t> candidates;
        candidates.reserve(k);

        for (int i = 0; i < k; ++i)
        {
            size_t candidateIndex;
            do
            {
                candidateIndex = rng->generateIndex(population.size());
            } while (candidateIndex == parent1Index);

            candidates.push_back(candidateIndex);
        }

        parent2Index = *std::min_element(
            candidates.begin(),
            candidates.end(),
            [this](size_t i, size_t j)
            { return population[i].fitness < population[j].fitness; });
    }

    return std::make_pair(population[parent1Index].solution,
                          population[parent2Index].solution);
}
