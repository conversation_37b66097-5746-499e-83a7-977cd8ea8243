#ifndef PROBLEMDATA_H
#define PROBLEMDATA_H

#include <fstream>
#include <memory>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

class Graph; // Forward declaration

using Node = int;
using NodeSet = std::unordered_set<Node>;
using Solution = NodeSet;

/**
 * ProblemData(num_nodes: int)
 *
 * Stores the data for a graph problem instance.
 *
 * Attributes
 * ----------
 * numNodes_
 *     The total number of nodes in the graph.
 * NodesSet
 *     A set of all nodes in the graph for fast lookup and iteration.
 * nodeWeight
 *     Stores the weight of each node, keyed by node ID.
 * AdjList
 *     The adjacency list representation of the graph; `AdjList[i]` stores all
 * neighbors of node `i`.
 */
class ProblemData
{
private:
    int numNodes_;
    NodeSet NodesSet;
    std::unordered_map<int, double>
        nodeWeight;
    std::vector<NodeSet> AdjList;

public:
    
    /**
     * ProblemData constructor.
     *
     * Parameters
     * ----------
     * num
     *     The number of nodes in the graph.
     */
    ProblemData(int num);

    /**
     * Reads problem data from an adjacency list file.
     *
     * Parameters
     * ----------
     * filename
     *     The name of the file containing the adjacency list data.
     *
     * Returns
     * -------
     * ProblemData
     *     An instance of ProblemData read from the file.
     */
    static ProblemData readFromAdjacencyListFile(const std::string &filename);

    /**
     * Reads problem data from an edge list format file.
     *
     * Parameters
     * ----------
     * filename
     *     The name of the file containing the edge list data.
     *
     * Returns
     * -------
     * ProblemData
     *     An instance of ProblemData read from the file.
     */
    static ProblemData readFromEdgeListFormat(const std::string &filename);

    /**
     * Returns the total number of nodes in the graph.
     */
    int numNodes() const { return numNodes_; }

    /**
     * Reads node weights from a file.
     *
     * Parameters
     * ----------
     * filename
     *     The name of the file containing the node weight data.
     */
    void readNodeWeightsFromFile(const std::string &filename);

    /**
     * Gets the weight of a specified node.
     *
     * Parameters
     * ----------
     * node
     *     The node to get the weight of.
     *
     * Returns
     * -------
     * double
     *     The weight of the node.
     */
    double getNodeWeight(Node node) const;

    /**
     * Gets the set of all nodes in the graph.
     *
     * Returns
     * -------
     * NodeSet
     *     A set of all nodes in the graph.
     */
    NodeSet getNodesSet() const { return NodesSet; }

    /**
     * Gets the adjacency list of the graph.
     *
     * Returns
     * -------
     * const std::vector<NodeSet>&
     *     The adjacency list of the graph.
     */
    const std::vector<NodeSet> &getAdjList() const { return AdjList; }

    /**
     * Gets the weights of all nodes.
     *
     * Returns
     * -------
     * const std::unordered_map<int, double>&
     *     A map containing the node weights.
     */
    const std::unordered_map<int, double> &getNodeWeights() const
    {
        return nodeWeight;
    }

    /**
     * Adds a node to the graph.
     *
     * Parameters
     * ----------
     * node
     *     The node to be added.
     */
    void addNode(Node node);

    /**
     * Adds a weight to a node.
     *
     * Parameters
     * ----------
     * node
     *     The node to add the weight to.
     * weight
     *     The weight of the node.
     */
    void addNodeWeight(Node node, double weight);

    /**
     * Adds an edge to the graph.
     *
     * Parameters
     * ----------
     * u
     *     One endpoint of the edge.
     * v
     *     The other endpoint of the edge.
     */
    void addEdge(Node u, Node v);

    /**
     * Creates a unique pointer to the original graph.
     *
     * Parameters
     * ----------
     * problemType
     *     The type of the problem.
     * numToRemove
     *     The number of nodes to remove.
     * seed
     *     The random seed.
     * K
     *     The parameter K.
     *
     * Returns
     * -------
     * std::unique_ptr<Graph>
     *     A unique pointer to the created graph.
     */
    std::unique_ptr<Graph> createOriginalGraph(const std::string &problemType,
                                               int numToRemove,
                                               int seed,
                                               int K) const;
};

#endif  // PROBLEMDATA_H