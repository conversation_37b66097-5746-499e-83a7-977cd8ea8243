#include "CBNSStrategy.h"
#include "Graph/CNP_Graph.h"
#include "Graph/NWCNP_Graph.h"
#include "SearchResult.h"
#include <iostream>
#include <stdexcept>

CBNSStrategy::CBNSStrategy(
    Graph &graph, const std::unordered_map<std::string, std::any> &params)
    : graph(graph), params(params)
{

    auto it = params.find("maxIdleSteps");
    if (it != params.end())
    {
        try
        {
            maxIdleSteps = std::any_cast<int>(it->second);
        }
        catch (const std::bad_any_cast &)
        {
        }
    }

    it = params.find("seed");
    if (it != params.end())
    {
        try
        {
            int seed = std::any_cast<int>(it->second);

            if (seed > 0)
            {
                rng.setSeed(seed);
            }
        }
        catch (const std::bad_any_cast &)
        {
        }
    }
}

SearchResult CBNSStrategy::execute()
{

    SearchResult result;

    Graph &currentGraph = graph;
    NodeSet bestSolution;

    if (auto *cnpGraph = dynamic_cast<CNP_Graph *>(&currentGraph))
    {
        bestSolution = cnpGraph->removedNodes;
    }
    else if (auto *nwcnpGraph = dynamic_cast<NWCNP_Graph *>(&currentGraph))
    {
        bestSolution = nwcnpGraph->removedNodes;
    }

    int currentObjValue = graph.getObjectiveValue();
    int bestObjValue = currentObjValue;
    long numIdleSteps = 0;
    long numSteps = 0;

    while (numIdleSteps < maxIdleSteps)
    {
        numSteps++;

        performMove(currentGraph, currentObjValue, numSteps);

        currentObjValue = currentGraph.getObjectiveValue();

        if (currentObjValue < bestObjValue)
        {
            bestSolution = currentGraph.getRemovedNodes(); // 现在返回const引用，会自动拷贝
            bestObjValue = currentObjValue;
            numIdleSteps = 0;
        }
        else
        {
            numIdleSteps++;
        }
    }

    result.solution = bestSolution;
    result.objValue = bestObjValue;

    return result;
}

void CBNSStrategy::performMove(Graph &currentGraph,
                               int &currentObjValue,
                               int numSteps)
{

    ComponentIndex componentToRemove = currentGraph.selectRemovedComponent();
    Node nodeToRemove
        = currentGraph.ageSelectNodeFromComponent(componentToRemove);

    currentGraph.removeNode(nodeToRemove);
    currentGraph.setNodeAge(nodeToRemove, numSteps);

    if (auto *nwcnpGraph = dynamic_cast<NWCNP_Graph *>(&currentGraph))
    {

        while (nwcnpGraph->SolWeight > nwcnpGraph->bound)
        {

            Node nodeToAdd = currentGraph.greedySelectNodeToAdd();

            currentGraph.addNode(nodeToAdd);

            currentGraph.setNodeAge(nodeToAdd, numSteps);
        }
    }
    else
    {

        Node nodeToAdd = currentGraph.greedySelectNodeToAdd();

        if (nodeToAdd != Graph::INVALID_NODE)
        {
            currentGraph.addNode(nodeToAdd);
            currentGraph.setNodeAge(nodeToAdd, numSteps);
        }
    }

    currentObjValue = currentGraph.getObjectiveValue();
}