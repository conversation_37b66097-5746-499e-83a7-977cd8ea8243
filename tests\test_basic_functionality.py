"""
基本功能测试

测试PyCNP的核心功能是否正常工作，包括模块导入、基本类创建等。
"""

import pytest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    import pycnp
    PYCNP_AVAILABLE = True
except ImportError as e:
    PYCNP_AVAILABLE = False
    IMPORT_ERROR = str(e)


class TestModuleImport:
    """测试模块导入功能"""
    
    def test_module_import(self):
        """测试主模块是否能正确导入"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        assert hasattr(pycnp, '__version__')
        assert hasattr(pycnp, 'Model')
        assert hasattr(pycnp, 'MemeticSearch')
    
    def test_constants_import(self):
        """测试常量是否正确导入"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        # 问题类型常量
        assert hasattr(pycnp, 'CNP')
        assert hasattr(pycnp, 'DCNP')
        assert hasattr(pycnp, 'NWCNP')
        
        # 搜索策略常量
        assert hasattr(pycnp, 'CBNS')
        assert hasattr(pycnp, 'CHNS')
        assert hasattr(pycnp, 'DLAS')
        assert hasattr(pycnp, 'BCLS')
        
        # 交叉策略常量
        assert hasattr(pycnp, 'DB')
        assert hasattr(pycnp, 'RSC')
        assert hasattr(pycnp, 'IRR')


class TestBasicClasses:
    """测试基本类的创建和功能"""
    
    def test_model_creation(self):
        """测试Model类的创建"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        model = pycnp.Model()
        assert model is not None
        assert hasattr(model, 'add_node')
        assert hasattr(model, 'add_edge')
    
    def test_memetic_search_params(self):
        """测试MemeticSearchParams类的创建"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        # 测试默认参数
        params = pycnp.MemeticSearchParams()
        assert params.initial_pop_size == 5
        assert params.is_pop_variable == True
        assert params.crossover == "RSC"

        # 测试自定义参数
        custom_params = pycnp.MemeticSearchParams(
            search="CHNS",
            crossover="DBX",
            is_pop_variable=False,
            initial_pop_size=10
        )
        assert custom_params.initial_pop_size == 10
        assert custom_params.is_pop_variable == False
        assert custom_params.crossover == "DBX"
    
    def test_stopping_criteria(self):
        """测试停止准则类"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        # 测试MaxRuntime
        max_runtime = pycnp.MaxRuntime(60)
        assert max_runtime is not None
        
        # 测试MaxIterations
        max_iterations = pycnp.MaxIterations(1000)
        assert max_iterations is not None
        
        # 测试NoImprovement
        no_improvement = pycnp.NoImprovement(100)
        assert no_improvement is not None


class TestErrorHandling:
    """测试错误处理"""
    
    def test_invalid_crossover_strategy(self):
        """测试无效的交叉策略"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")
        
        with pytest.raises(ValueError):
            pycnp.MemeticSearchParams(crossover="INVALID")
    
    def test_invalid_parameters(self):
        """测试无效参数"""
        if not PYCNP_AVAILABLE:
            pytest.skip(f"PyCNP module not available: {IMPORT_ERROR}")

        # 目前参数验证主要针对crossover策略
        # 可以在未来添加更多的参数验证
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
