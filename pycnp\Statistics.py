import csv
import time
from dataclasses import dataclass, field, fields
from math import nan
from pathlib import Path
from typing import List, Optional

@dataclass
class _IterationStats:
    """
    Stores statistics for a single iteration.
    """
    runtime: float
    best_obj_value: float
    population_size: int
    num_idle_generations: int

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, _IterationStats):
            return False
        # Handle potential NaN comparisons if needed in the future
        return (self.runtime == other.runtime and
                self.best_obj_value == other.best_obj_value and
                self.population_size == other.population_size and
                self.num_idle_generations == other.num_idle_generations)


class Statistics:
    """
    Tracks statistics during a MemeticSearch run.
    """
    
    data: List[_IterationStats] = field(default_factory=list)
    """List of :class:`._IterationStats` for each recorded iteration."""
    
    num_iterations: int = 0
    """Total number of iterations for which statistics were collected."""

    def __init__(self, collect_stats: bool = True):
        self._collect_stats = collect_stats
        self._clock = time.perf_counter()
        self.data = []
        self.num_iterations = 0

    def __eq__(self, other: object) -> bool:
        return (isinstance(other, Statistics) and
                self._collect_stats == other._collect_stats and
                self.num_iterations == other.num_iterations and
                self.data == other.data)

    def is_collecting(self) -> bool:
        """
        Returns whether statistics are being collected.
        """
        return self._collect_stats

    def collect(self, best_obj_value: float, population_size: int, num_idle_generations: int):
        """
        Collects statistics for the current iteration.
        """
        if not self._collect_stats:
            return

        now = time.perf_counter()
        runtime = now - self._clock
        self._clock = now
        
        self.num_iterations += 1
        datum = _IterationStats(
            runtime=runtime,
            best_obj_value=best_obj_value,
            population_size=population_size,
            num_idle_generations=num_idle_generations
        )
        self.data.append(datum)
        
    @property
    def runtimes(self) -> List[float]:
        """Returns a list of runtimes for each iteration."""
        return [d.runtime for d in self.data]

    def to_csv(self, where: Path | str, delimiter: str = ",", quoting: int = csv.QUOTE_MINIMAL, **kwargs):
        """
        Writes statistics to a CSV file.
        """
        if not self._collect_stats or not self.data:
            print("No statistics collected or data is empty, skipping CSV export.")
            return
            
        field_names = [f.name for f in fields(_IterationStats)]

        with open(where, "w", newline='') as fh:
            writer = csv.DictWriter(fh, fieldnames=field_names, delimiter=delimiter, quoting=quoting, **kwargs)
            writer.writeheader()
            for datum in self.data:
                writer.writerow(vars(datum))

    @classmethod
    def from_csv(cls, where: Path | str, delimiter: str = ",", **kwargs):
        """
        Reads statistics from a CSV file.
        """
        stats = cls(collect_stats=True) # Assume collecting if loading
        field_types = {f.name: f.type for f in fields(_IterationStats)}

        try:
            with open(where, 'r') as fh:
                reader = csv.DictReader(fh, delimiter=delimiter, **kwargs)
                for row in reader:
                    datum_values = {}
                    for name, type_hint in field_types.items():
                        if name in row:
                            try:
                                datum_values[name] = type_hint(row[name])
                            except (ValueError, TypeError):
                                datum_values[name] = nan # Or handle error as appropriate
                        else:
                             datum_values[name] = nan # Handle missing columns
                             
                    stats.data.append(_IterationStats(**datum_values))
                    stats.num_iterations += 1
            stats._collect_stats = True # Ensure flag is set correctly after loading
        except FileNotFoundError:
            print(f"Error: File not found at {where}")
            # Return an empty Statistics object or raise an error
            return cls(collect_stats=False) 
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            # Return an empty Statistics object or raise an error
            return cls(collect_stats=False)
            
        return stats
