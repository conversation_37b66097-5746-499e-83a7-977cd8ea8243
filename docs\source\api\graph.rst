.. module:: pycnp._pycnp
   :synopsis: Graph data structures and algorithms for Critical Node Problems

Graph Data Structures
======================

The :mod:`pycnp._pycnp` module provides a comprehensive hierarchy of graph classes
specifically designed for different variants of Critical Node Problems. These high-performance
C++ implementations offer efficient graph operations, connectivity analysis, and
objective function evaluation optimized for large-scale network optimization.

Each graph class encapsulates both the network topology and problem-specific algorithms,
providing a clean interface for algorithm development and deployment.

Graph Class Hierarchy
---------------------

Base Graph Class
~~~~~~~~~~~~~~~~

.. autoclass:: pycnp._pycnp.Graph
   :members:
   :undoc-members:
   :show-inheritance:
   :exclude-members: __init__

   Abstract base class defining the common interface for all graph implementations.
   Provides fundamental graph operations and connectivity analysis methods.

   This class is not instantiated directly; use one of the specialized derived classes
   based on your problem type.

Core Graph Operations
~~~~~~~~~~~~~~~~~~~~~

The following methods are available on all graph instances:

**Graph Structure Access**

.. automethod:: pycnp._pycnp.Graph.get_num_nodes

   Returns the total number of nodes in the graph.

.. automethod:: pycnp._pycnp.Graph.get_num_edges

   Returns the total number of edges in the graph.

**Solution Management**

.. automethod:: pycnp._pycnp.Graph.get_removed_nodes

   Returns the set of currently removed nodes.

.. automethod:: pycnp._pycnp.Graph.set_removed_nodes

   Sets the nodes to be removed from the graph.

   **Parameters:**

   - ``nodes`` (Set[int]): Set of node IDs to remove

.. automethod:: pycnp._pycnp.Graph.is_node_removed

   Checks if a specific node is currently marked for removal.

   **Parameters:**

   - ``node_id`` (int): Node ID to check

   **Returns:**

   - ``bool``: True if the node is removed, False otherwise

**Connectivity Analysis**

.. automethod:: pycnp._pycnp.Graph.get_num_connected_components

   Returns the number of connected components in the residual graph
   (after removing the specified nodes).

.. automethod:: pycnp._pycnp.Graph.get_largest_component_size

   Returns the size of the largest connected component in the residual graph.

**Objective Function Evaluation**

.. automethod:: pycnp._pycnp.Graph.get_objective_value

   Computes and returns the objective function value for the current solution.
   The specific objective depends on the problem type (CNP, DCNP, or NWCNP).

**Graph Manipulation**

.. automethod:: pycnp._pycnp.Graph.clone

   Creates a deep copy of the graph instance with the same structure and state.

.. automethod:: pycnp._pycnp.Graph.get_random_feasible_graph

   Generates a random feasible solution within the problem constraints.

   **Parameters:**

   - ``bound``: Maximum number of nodes to remove (or weight budget for NWCNP)
   - ``seed`` (optional): Random seed for reproducibility

Specialized Graph Classes
-------------------------

The following classes implement problem-specific logic for different CNP variants.
All classes inherit the common interface from the base Graph class.

CNP Graph
~~~~~~~~~

.. autoclass:: pycnp._pycnp.CNP_Graph
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

   **Standard Critical Node Problem Graph**

   Implements the classic CNP formulation where the objective is to minimize
   the total pairwise connectivity in the residual graph. This is the most
   commonly used variant for general connectivity disruption problems.

   **Objective Function:**

   .. math::

      f(S) = \sum_{i=1}^{M} \frac{|C_i|(|C_i| - 1)}{2}

   where :math:`C_i` are the connected components in the residual graph and
   :math:`M` is the total number of components.

   **Use Cases:**

   - Social network disruption
   - Infrastructure vulnerability analysis
   - General network robustness studies

DCNP Graph
~~~~~~~~~~

.. autoclass:: pycnp._pycnp.DCNP_Graph
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

   **Distance-Constrained Critical Node Problem Graph**

   Implements the DCNP variant where only node pairs within a specified distance
   contribute to the objective function. This allows for modeling local connectivity
   effects and distance-sensitive applications.

   **Objective Function:**

   .. math::

      f(S) = \sum_{i,j \in V \setminus S, i<j} \psi(d(i,j))

   where :math:`\psi(d(i,j)) = 1` if :math:`d(i,j) \leq k`, and 0 otherwise.

   **Specialized Methods:**

   .. automethod:: pycnp._pycnp.DCNP_Graph.calculate_khop_tree_size

      Calculates the size of the k-hop tree rooted at a specific node.

      **Parameters:**

      - ``root_node`` (int): Root node for tree calculation
      - ``k`` (int): Maximum hop distance

      **Returns:**

      - ``int``: Number of nodes within k hops of the root

   .. automethod:: pycnp._pycnp.DCNP_Graph.calculate_betweenness_centrality

      Computes betweenness centrality measures for distance-constrained paths.

      **Returns:**

      - ``Dict[int, float]``: Mapping from node IDs to centrality values

   .. automethod:: pycnp._pycnp.DCNP_Graph.find_best_node_to_remove

      Identifies the node whose removal would most improve the objective function.

      **Returns:**

      - ``int``: Node ID of the best candidate for removal

   .. automethod:: pycnp._pycnp.DCNP_Graph.find_best_node_to_add

      Identifies the best node to add back to the current solution.

      **Returns:**

      - ``int``: Node ID of the best candidate for addition

   **Use Cases:**

   - Local network disruption
   - Epidemic control with limited transmission range
   - Wireless network optimization

NWCNP Graph
~~~~~~~~~~~

.. autoclass:: pycnp._pycnp.NWCNP_Graph
   :members:
   :undoc-members:
   :show-inheritance:
   :special-members: __init__

   **Node-Weighted Critical Node Problem Graph**

   Implements the NWCNP variant where nodes have associated weights and the
   constraint is on the total weight budget rather than node count. This enables
   more realistic modeling of removal costs and resource constraints.

   **Constraint:**

   .. math::

      \sum_{v \in S} w_v \leq B

   where :math:`w_v` is the weight of node :math:`v` and :math:`B` is the budget.

   **Weight Management Methods:**

   .. automethod:: pycnp._pycnp.NWCNP_Graph.get_node_weight

      Returns the weight of a specific node.

      **Parameters:**

      - ``node_id`` (int): Node identifier

      **Returns:**

      - ``float``: Weight of the specified node

   .. automethod:: pycnp._pycnp.NWCNP_Graph.get_total_node_weight

      Returns the sum of all node weights in the graph.

      **Returns:**

      - ``float``: Total weight of all nodes

   .. automethod:: pycnp._pycnp.NWCNP_Graph.get_sum_of_weights_of_removed_nodes

      Returns the total weight of currently removed nodes.

      **Returns:**

      - ``float``: Sum of weights of removed nodes

   .. note::

      The ``get_objective_value()`` method returns the connectivity-based objective
      (same as CNP), not the weight sum. Use ``get_sum_of_weights_of_removed_nodes()``
      to check constraint satisfaction.

   **Use Cases:**

   - Resource-constrained network protection
   - Cost-aware infrastructure hardening
   - Budget-limited intervention strategies

Usage Examples
--------------

Basic Graph Operations
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import pycnp
   from pycnp._pycnp import CNP_Graph

   # Load problem data
   problem_data = pycnp.read("graph.txt")

   # Create CNP graph
   graph = problem_data.create_graph("CNP")

   # Basic graph information
   print(f"Nodes: {graph.get_num_nodes()}")
   print(f"Edges: {graph.get_num_edges()}")

   # Set a solution (remove nodes 1, 3, 5)
   solution = {1, 3, 5}
   graph.set_removed_nodes(solution)

   # Evaluate solution
   objective = graph.get_objective_value()
   components = graph.get_num_connected_components()
   largest = graph.get_largest_component_size()

   print(f"Objective value: {objective}")
   print(f"Connected components: {components}")
   print(f"Largest component size: {largest}")

Working with NWCNP
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Load weighted graph data
   problem_data = pycnp.read("graph.txt", "weights.txt")
   nwcnp_graph = problem_data.create_graph("NWCNP")

   # Check node weights
   for node_id in range(nwcnp_graph.get_num_nodes()):
       weight = nwcnp_graph.get_node_weight(node_id)
       print(f"Node {node_id}: weight = {weight}")

   # Set solution and check constraint
   solution = {0, 2, 4}
   nwcnp_graph.set_removed_nodes(solution)

   used_weight = nwcnp_graph.get_sum_of_weights_of_removed_nodes()
   budget = 10.0

   if used_weight <= budget:
       objective = nwcnp_graph.get_objective_value()
       print(f"Feasible solution: objective = {objective}")
   else:
       print(f"Infeasible: weight {used_weight} exceeds budget {budget}")

Distance-Constrained Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Create DCNP graph with distance threshold
   dcnp_graph = problem_data.create_graph("DCNP")

   # Analyze k-hop neighborhoods
   for node_id in range(min(5, dcnp_graph.get_num_nodes())):
       tree_size = dcnp_graph.calculate_khop_tree_size(node_id, k=3)
       print(f"Node {node_id}: 3-hop neighborhood size = {tree_size}")

   # Find best nodes for removal/addition
   best_remove = dcnp_graph.find_best_node_to_remove()
   best_add = dcnp_graph.find_best_node_to_add()

   print(f"Best node to remove: {best_remove}")
   print(f"Best node to add: {best_add}")

Performance Considerations
--------------------------

**Memory Efficiency**

- All graph classes use sparse representations for large networks
- Connectivity information is computed incrementally
- Memory usage scales linearly with graph size

**Computational Complexity**

- **CNP**: O(|V| + |E|) for objective evaluation
- **DCNP**: O(|V|²) for distance-constrained evaluation
- **NWCNP**: O(|V| + |E|) plus O(|S|) for weight computation

**Optimization Tips**

- Reuse graph instances when possible to avoid reconstruction overhead
- Use ``clone()`` for creating modified copies efficiently
- Batch solution evaluations when comparing multiple candidates

.. tip::

   For large-scale problems, consider using CNP_Graph as it has the most efficient
   objective function evaluation. DCNP_Graph should be used only when distance
   constraints are essential to the problem formulation.