# PyCharm Windows 环境配置指南

## 1. 项目导入和配置

### 1.1 打开项目
1. 启动PyCharm
2. 选择 `File` → `Open`
3. 选择PyCNP项目文件夹
4. 点击 `OK`

### 1.2 配置Python解释器
1. 打开 `File` → `Settings` (或 `Ctrl+Alt+S`)
2. 导航到 `Project: PyCNP` → `Python Interpreter`
3. 点击齿轮图标 → `Add...`
4. 选择 `System Interpreter`
5. 选择您的Python安装路径（确保是3.8+版本）
6. 点击 `OK`

### 1.3 配置项目结构
1. 打开 `File` → `Settings`
2. 导航到 `Project: PyCNP` → `Project Structure`
3. 将项目根目录标记为 `Sources Root`（右键点击项目根目录 → `Mark as` → `Sources Root`）
4. 确保 `pycnp` 文件夹也被标记为源码目录

## 2. 依赖安装

### 2.1 在PyCharm终端中安装依赖
1. 打开PyCharm底部的 `Terminal` 标签
2. 执行以下命令：
```bash
# 安装基础依赖
pip install numpy>=1.19.0 meson-python pybind11 meson ninja

# 如果需要开发依赖
pip install pytest black isort mypy flake8
```

### 2.2 构建C++扩展
在PyCharm终端中执行：
```bash
# 运行构建脚本
rebuild.bat
```

## 3. 运行配置

### 3.1 创建运行配置
1. 右键点击 `examples/model-CNP.py`
2. 选择 `Run 'model-CNP'`
3. 或者点击顶部菜单 `Run` → `Run...` → 选择要运行的文件

### 3.2 配置工作目录
如果遇到文件路径问题：
1. 点击 `Run` → `Edit Configurations...`
2. 在 `Working directory` 中设置为项目根目录路径
3. 确保 `Script path` 指向正确的Python文件

### 3.3 环境变量配置（如果需要）
1. 在 `Run/Debug Configurations` 中
2. 展开 `Environment variables`
3. 添加 `PYTHONPATH` 变量，值为项目根目录路径

## 4. 调试配置

### 4.1 设置断点
1. 在代码行号左侧点击设置断点
2. 右键点击Python文件选择 `Debug 'filename'`

### 4.2 调试C++代码（高级）
如果需要调试C++部分：
1. 确保编译时包含调试信息
2. 在 `meson.build` 中添加调试标志
3. 使用Mixed-Mode调试（需要Visual Studio）

## 5. 常见问题解决

### 5.1 模块导入错误
```python
# 如果出现 "ModuleNotFoundError: No module named 'pycnp'"
# 在代码开头添加：
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)) + '/..')
```

### 5.2 C++编译错误
- 确保安装了Visual Studio Build Tools
- 检查Python版本兼容性
- 重新运行 `rebuild.bat`

### 5.3 文件路径问题
- 确保测试数据文件在正确位置
- 使用相对路径或绝对路径
- 检查工作目录设置

### 5.4 权限问题
- 以管理员身份运行PyCharm（如果需要）
- 检查文件夹权限设置

## 6. 测试运行

### 6.1 验证安装
在PyCharm控制台中运行：
```python
import pycnp
print("PyCNP version:", pycnp.__version__ if hasattr(pycnp, '__version__') else "Unknown")
print("Available modules:", dir(pycnp))
```

### 6.2 运行示例
1. 打开 `examples/model-CNP.py`
2. 确保数据文件路径正确
3. 点击运行按钮或按 `Ctrl+Shift+F10`

## 7. 性能优化建议

### 7.1 PyCharm设置优化
1. 增加内存分配：`Help` → `Edit Custom VM Options`
2. 添加：`-Xmx4g` （根据您的内存情况调整）

### 7.2 Python优化
1. 使用Release模式编译C++扩展
2. 在 `meson.build` 中设置优化标志

## 8. 故障排除清单

- [ ] Python版本 >= 3.8
- [ ] Visual Studio Build Tools已安装
- [ ] 所有依赖包已安装
- [ ] C++扩展编译成功
- [ ] 项目结构配置正确
- [ ] 工作目录设置正确
- [ ] 数据文件路径正确
- [ ] 环境变量配置（如果需要）
