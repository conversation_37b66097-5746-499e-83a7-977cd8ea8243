#ifndef SEARCH_STRATEGY_H
#define SEARCH_STRATEGY_H

#include "../SearchResult.h"  
#include <string>             

/**
 * SearchStrategy
 *
 * Search strategy interface.
 *
 * All concrete search algorithms need to implement this interface.
 */
class SearchStrategy
{
public:
   
    virtual ~SearchStrategy() = default;

    /**
     * Run search algorithm.
     *
     * Returns
     * -------
     * SearchResult
     *     Search result.
     */
    virtual SearchResult execute() = 0;

    /**
     * Get strategy name.
     *
     * Returns
     * -------
     * std::string
     *     Strategy name.
     */
    virtual std::string getName() const = 0;
};

#endif  // SEARCH_STRATEGY_H