[build-system]
requires = ["meson-python", "pybind11"]
build-backend = "mesonpy"

[project]
name = "pycnp"
version = "0.1.1.dev0"
description = "高性能关键节点问题求解器的Python绑定"
authors = [{name = "<PERSON>", email = "<EMAIL>"}]
readme = "README.md"
license = {text = "MIT"}
keywords = ["graph", "optimization", "critical-node-problem", "algorithms"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: C++",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8.1"
dependencies = [
    "numpy>=1.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "black>=23.0",
    "isort>=5.12",
    "mypy>=1.0",
    "flake8>=6.0",
    "pre-commit>=3.0",
    "meson>=1.0",
    "ninja>=1.11",
    "sphinx>=6.0",
    "sphinx-rtd-theme>=1.2",
    "sphinx-immaterial>=0.11.0",
    "myst-parser>=2.0",
    "sphinx-autobuild>=2021.3.14",
    "numpydoc>=1.5.0",
    "nbsphinx>=0.9.0",
    "tomli>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/xuebo100/PyCNP"
Repository = "https://github.com/xuebo100/PyCNP.git"
Documentation = "https://github.com/xuebo100/PyCNP"
"Bug Tracker" = "https://github.com/xuebo100/PyCNP/issues"

# 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除构建目录
  build/
  | dist/
  | \.git/
  | \.mypy_cache/
  | \.pytest_cache/
  | __pycache__/
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["pycnp"]
known_third_party = ["numpy", "pytest"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# Pytest配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

# Coverage配置
[tool.coverage.run]
source = ["pycnp"]
omit = [
    "*/tests/*",
    "*/build/*",
    "*/dist/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]


